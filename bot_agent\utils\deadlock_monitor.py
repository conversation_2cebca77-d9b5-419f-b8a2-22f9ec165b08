"""
死循环检测和监控系统
当系统进入死循环时，强制输出日志并提供调试信息
"""

import threading
import time
import traceback
import sys
import os
import signal
from typing import Dict, Any, Optional, Callable
from dataclasses import dataclass
from datetime import datetime

from bot_agent.utils.logging_config import get_logger

logger = get_logger(__name__)


@dataclass
class MonitorPoint:
    """监控点"""
    name: str
    start_time: float
    last_update: float
    call_count: int
    max_duration: float
    stack_trace: str


class DeadlockMonitor:
    """
    死循环检测监控器

    功能：
    1. 监控方法执行时间
    2. 检测可能的死循环
    3. 强制输出日志
    4. 提供紧急退出机制
    """

    def __init__(self, check_interval: float = 2.0, max_duration: float = 30.0):
        """
        初始化监控器

        Args:
            check_interval: 检查间隔（秒）
            max_duration: 最大允许执行时间（秒）
        """
        self.check_interval = check_interval
        self.max_duration = max_duration
        self.monitor_points: Dict[str, MonitorPoint] = {}
        self.monitoring = False
        self.monitor_thread: Optional[threading.Thread] = None
        self.lock = threading.Lock()

        # 设置信号处理器
        self._setup_signal_handlers()

        logger.info(f"DeadlockMonitor初始化完成，检查间隔: {check_interval}s，最大执行时间: {max_duration}s")

    def _setup_signal_handlers(self):
        """设置信号处理器"""
        try:
            # 设置SIGINT处理器（Ctrl+C）
            signal.signal(signal.SIGINT, self._emergency_exit)

            # 在Windows上设置SIGBREAK处理器（Ctrl+Break）
            if hasattr(signal, 'SIGBREAK'):
                signal.signal(signal.SIGBREAK, self._emergency_exit)

            logger.info("信号处理器设置完成")
        except Exception as e:
            logger.warning(f"设置信号处理器失败: {e}")

    def _emergency_exit(self, signum, frame):
        """紧急退出处理器"""
        print("\n" + "="*60)
        print("🚨 检测到紧急退出信号！")
        print("="*60)

        # 输出当前监控状态
        self._force_log_status()

        # 输出调用栈
        print("\n📍 当前调用栈:")
        traceback.print_stack(frame)

        print("\n💀 强制退出程序...")
        os._exit(1)

    def start_monitoring(self):
        """开始监控"""
        if self.monitoring:
            return

        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        logger.info("死循环监控已启动")

    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=1.0)
        logger.info("死循环监控已停止")

    def register_point(self, name: str, max_duration: Optional[float] = None) -> str:
        """
        注册监控点

        Args:
            name: 监控点名称
            max_duration: 该监控点的最大执行时间

        Returns:
            str: 监控点ID
        """
        current_time = time.time()
        stack_trace = ''.join(traceback.format_stack())

        with self.lock:
            if name in self.monitor_points:
                # 更新现有监控点
                point = self.monitor_points[name]
                point.last_update = current_time
                point.call_count += 1
            else:
                # 创建新监控点
                point = MonitorPoint(
                    name=name,
                    start_time=current_time,
                    last_update=current_time,
                    call_count=1,
                    max_duration=max_duration or self.max_duration,
                    stack_trace=stack_trace
                )
                self.monitor_points[name] = point

        # 强制输出日志
        self._force_log(f"📍 注册监控点: {name}")
        return name

    def update_point(self, name: str, message: str = ""):
        """
        更新监控点

        Args:
            name: 监控点名称
            message: 更新消息
        """
        current_time = time.time()

        with self.lock:
            if name in self.monitor_points:
                self.monitor_points[name].last_update = current_time

        # 强制输出日志
        self._force_log(f"🔄 更新监控点: {name} - {message}")

    def unregister_point(self, name: str):
        """
        注销监控点

        Args:
            name: 监控点名称
        """
        with self.lock:
            if name in self.monitor_points:
                point = self.monitor_points.pop(name)
                duration = time.time() - point.start_time

        # 强制输出日志
        self._force_log(f"✅ 注销监控点: {name}，执行时间: {duration:.2f}s")

    def _monitor_loop(self):
        """监控循环"""
        while self.monitoring:
            try:
                current_time = time.time()

                with self.lock:
                    for name, point in list(self.monitor_points.items()):
                        duration = current_time - point.start_time
                        idle_time = current_time - point.last_update

                        # 检查是否超时
                        if duration > point.max_duration:
                            self._handle_timeout(name, point, duration)

                        # 检查是否长时间无更新（增加阈值，避免服务器启动时误报）
                        elif idle_time > max(self.check_interval * 5, 10.0):  # 至少10秒
                            # 检查是否在服务器启动阶段
                            if not self._is_server_startup_phase():
                                self._handle_idle(name, point, idle_time)

                time.sleep(self.check_interval)

            except Exception as e:
                self._force_log(f"❌ 监控循环出错: {e}")
                time.sleep(1.0)

    def _handle_timeout(self, name: str, point: MonitorPoint, duration: float):
        """处理超时"""
        self._force_log(f"🚨 检测到可能的死循环！")
        self._force_log(f"   监控点: {name}")
        self._force_log(f"   执行时间: {duration:.2f}s")
        self._force_log(f"   调用次数: {point.call_count}")
        self._force_log(f"   最大允许时间: {point.max_duration}s")

        # 输出调用栈
        self._force_log(f"📍 调用栈:")
        for line in point.stack_trace.split('\n'):
            if line.strip():
                self._force_log(f"   {line}")

        # 输出当前状态
        self._force_log_status()

        # 可选：强制退出
        if duration > point.max_duration * 2:
            self._force_log(f"💀 执行时间过长，强制退出程序！")
            os._exit(1)

    def _is_server_startup_phase(self) -> bool:
        """检查是否在服务器启动阶段"""
        try:
            import traceback

            # 获取当前调用栈
            stack = traceback.extract_stack()

            # 检查调用栈中是否包含服务器启动相关的模块
            startup_indicators = [
                'uvicorn',
                'multiprocessing',
                'subprocess_started',
                'asyncio.run',
                '_bootstrap',
                'spawn.py'
            ]

            for frame in stack:
                frame_info = f"{frame.filename}:{frame.name}"
                if any(indicator in frame_info for indicator in startup_indicators):
                    return True

            return False
        except Exception:
            # 如果检测失败，保守地返回False
            return False

    def _handle_idle(self, name: str, point: MonitorPoint, idle_time: float):
        """处理长时间无更新"""
        self._force_log(f"⚠️  监控点长时间无更新: {name}")
        self._force_log(f"   无更新时间: {idle_time:.2f}s")
        self._force_log(f"   可能卡在某个操作上")

    def _force_log(self, message: str):
        """强制输出日志"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_message = f"[{timestamp}] [DEADLOCK_MONITOR] {message}"

        # 同时输出到控制台和日志文件
        print(log_message, flush=True)
        logger.info(message)

        # 强制刷新
        sys.stdout.flush()
        sys.stderr.flush()

    def _force_log_status(self):
        """强制输出当前状态"""
        self._force_log("📊 当前监控状态:")

        with self.lock:
            if not self.monitor_points:
                self._force_log("   无活跃监控点")
                return

            for name, point in self.monitor_points.items():
                current_time = time.time()
                duration = current_time - point.start_time
                idle_time = current_time - point.last_update

                self._force_log(f"   {name}:")
                self._force_log(f"     执行时间: {duration:.2f}s")
                self._force_log(f"     无更新时间: {idle_time:.2f}s")
                self._force_log(f"     调用次数: {point.call_count}")


# 全局监控器实例
global_deadlock_monitor = DeadlockMonitor()


def monitor_method(name: str, max_duration: Optional[float] = None):
    """
    方法监控装饰器

    Args:
        name: 监控点名称
        max_duration: 最大执行时间
    """
    def decorator(func: Callable):
        def wrapper(*args, **kwargs):
            monitor_id = global_deadlock_monitor.register_point(name, max_duration)
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                global_deadlock_monitor.unregister_point(monitor_id)
        return wrapper
    return decorator


def start_global_monitoring():
    """启动全局监控"""
    global_deadlock_monitor.start_monitoring()


def stop_global_monitoring():
    """停止全局监控"""
    global_deadlock_monitor.stop_monitoring()


# 上下文管理器
class MonitorContext:
    """监控上下文管理器"""

    def __init__(self, name: str, max_duration: Optional[float] = None):
        self.name = name
        self.max_duration = max_duration
        self.monitor_id = None

    def __enter__(self):
        self.monitor_id = global_deadlock_monitor.register_point(self.name, self.max_duration)
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.monitor_id:
            global_deadlock_monitor.unregister_point(self.monitor_id)

    def update(self, message: str = ""):
        """更新监控点"""
        if self.monitor_id:
            global_deadlock_monitor.update_point(self.monitor_id, message)
