#!/usr/bin/env python3
"""
测试SyncToolCoordinator修复
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_sync_coordinator_methods():
    """测试SyncToolCoordinator方法访问"""
    print("🔧 测试SyncToolCoordinator方法访问...")
    
    try:
        from bot_agent.tools.intelligent_tool_coordinator import global_tool_coordinator
        
        print("✅ global_tool_coordinator导入成功")
        
        # 测试SyncToolCoordinator的基本方法
        basic_methods = [
            'get_job_info_and_log',
            'analyze_job_errors',
            'execute_targeted_fixes',
            'verify_fixes'
        ]
        
        for method_name in basic_methods:
            if hasattr(global_tool_coordinator, method_name):
                print(f"✅ SyncToolCoordinator.{method_name} 存在")
            else:
                print(f"❌ SyncToolCoordinator.{method_name} 不存在")
                return False
        
        # 测试async_coordinator属性
        if hasattr(global_tool_coordinator, 'async_coordinator'):
            print("✅ SyncToolCoordinator.async_coordinator 存在")
            
            # 测试async_coordinator的多轮交互方法
            async_methods = [
                '_ai_generate_fix_plan',
                '_execute_ai_fix_step',
                '_execute_command_with_retry',
                '_get_ai_alternative_command',
                '_categorize_errors'
            ]
            
            for method_name in async_methods:
                if hasattr(global_tool_coordinator.async_coordinator, method_name):
                    print(f"✅ IntelligentToolCoordinator.{method_name} 存在")
                else:
                    print(f"❌ IntelligentToolCoordinator.{method_name} 不存在")
                    return False
        else:
            print("❌ SyncToolCoordinator.async_coordinator 不存在")
            return False
        
        print("🎉 所有方法访问测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ SyncToolCoordinator方法访问测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_task_executor_integration():
    """测试TaskExecutor集成"""
    print("\n🔄 测试TaskExecutor集成...")
    
    try:
        from bot_agent.engines.task_executor import AiderBasedTaskExecutor
        
        executor = AiderBasedTaskExecutor()
        print("✅ AiderBasedTaskExecutor初始化成功")
        
        # 测试多轮交互修复方法
        multi_round_methods = [
            '_execute_multi_round_intelligent_fix',
            '_multi_round_strategy_fix',
            '_generate_alternative_fix',
            '_multi_round_lint_fix',
            '_multi_round_dependency_fix',
            '_multi_round_build_fix',
            '_multi_round_generic_fix'
        ]
        
        for method_name in multi_round_methods:
            if hasattr(executor, method_name):
                print(f"✅ TaskExecutor.{method_name} 存在")
            else:
                print(f"❌ TaskExecutor.{method_name} 不存在")
                return False
        
        print("✅ TaskExecutor集成测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ TaskExecutor集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_method_call_simulation():
    """测试方法调用模拟"""
    print("\n🧪 测试方法调用模拟...")
    
    try:
        from bot_agent.tools.intelligent_tool_coordinator import global_tool_coordinator
        
        # 模拟检查方法调用路径
        test_cases = [
            "global_tool_coordinator.async_coordinator._ai_generate_fix_plan",
            "global_tool_coordinator.async_coordinator._execute_ai_fix_step",
            "global_tool_coordinator.async_coordinator._execute_command_with_retry",
            "global_tool_coordinator.async_coordinator._get_ai_alternative_command",
            "global_tool_coordinator.async_coordinator._categorize_errors"
        ]
        
        for test_case in test_cases:
            try:
                # 使用eval检查方法路径是否可访问
                method = eval(test_case)
                if callable(method):
                    print(f"✅ {test_case} 可访问且可调用")
                else:
                    print(f"❌ {test_case} 不可调用")
                    return False
            except AttributeError as e:
                print(f"❌ {test_case} 属性错误: {e}")
                return False
            except Exception as e:
                print(f"❌ {test_case} 其他错误: {e}")
                return False
        
        print("✅ 方法调用模拟测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 方法调用模拟测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🧪 SyncToolCoordinator修复验证测试")
    print("=" * 50)
    
    tests = [
        ("SyncToolCoordinator方法访问", test_sync_coordinator_methods),
        ("TaskExecutor集成", test_task_executor_integration),
        ("方法调用模拟", test_method_call_simulation),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔬 执行 {test_name}测试...")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            import traceback
            traceback.print_exc()
            results.append((test_name, False))
    
    # 汇总结果
    print("\n📊 测试结果汇总:")
    print("-" * 40)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！SyncToolCoordinator修复成功。")
        print("\n💡 修复完成的问题：")
        print("1. ✅ 修复了'SyncToolCoordinator' object has no attribute '_ai_generate_fix_plan'错误")
        print("2. ✅ 正确配置了async_coordinator方法访问路径")
        print("3. ✅ 多轮交互修复功能现在可以正常工作")
        print("4. ✅ TaskExecutor可以正确调用多轮交互方法")
        print("\n🎯 解决的核心问题：")
        print("- 🔴 方法缺失错误 → ✅ 正确的方法访问路径")
        print("- 🔴 同步包装器不完整 → ✅ 完整的异步方法访问")
        print("- 🔴 多轮修复无法执行 → ✅ 多轮交互修复正常工作")
        print("\n🚀 现在task_1748437771_1748437771类似的任务应该能够成功执行多轮修复！")
    else:
        print("⚠️ 部分测试失败，修复可能不完整。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
