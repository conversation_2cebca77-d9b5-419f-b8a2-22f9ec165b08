"""
项目上下文收集器
为AI多轮交互提供丰富的项目上下文信息，帮助AI更准确地生成修复命令
"""

import os
import json
import platform
import subprocess
import sys
from pathlib import Path
from typing import Dict, Any, List, Optional
import logging

from bot_agent.utils.logging_config import get_logger

logger = get_logger(__name__)


class ProjectContextCollector:
    """
    项目上下文收集器
    
    收集项目的各种上下文信息，为AI提供丰富的背景知识：
    1. 项目基础信息（类型、框架、版本等）
    2. 目录结构信息
    3. 运行环境信息
    4. 配置文件内容
    5. 错误上下文信息
    """
    
    def __init__(self):
        self.context_cache = {}
        
    async def collect_full_context(self, project_path: str, error_info: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        收集完整的项目上下文信息
        
        Args:
            project_path: 项目路径
            error_info: 错误信息（可选）
            
        Returns:
            完整的上下文信息字典
        """
        try:
            logger.info(f"🔍 开始收集项目上下文信息: {project_path}")
            
            context = {
                'project_path': project_path,
                'collection_timestamp': self._get_timestamp(),
                'project_info': await self._collect_project_info(project_path),
                'directory_structure': await self._collect_directory_structure(project_path),
                'environment_info': await self._collect_environment_info(project_path),
                'config_files': await self._collect_config_files(project_path),
                'dependencies': await self._collect_dependencies(project_path),
                'git_info': await self._collect_git_info(project_path),
                'error_context': error_info or {}
            }
            
            # 缓存上下文信息
            self.context_cache[project_path] = context
            
            logger.info(f"✅ 项目上下文收集完成，共收集 {len(context)} 个维度的信息")
            return context
            
        except Exception as e:
            logger.error(f"项目上下文收集失败: {e}")
            return {
                'project_path': project_path,
                'error': str(e),
                'collection_timestamp': self._get_timestamp()
            }
    
    async def _collect_project_info(self, project_path: str) -> Dict[str, Any]:
        """收集项目基础信息"""
        try:
            project_info = {
                'name': os.path.basename(project_path),
                'type': 'unknown',
                'framework': [],
                'language': 'unknown'
            }
            
            # 检测项目类型和语言
            if os.path.exists(os.path.join(project_path, 'requirements.txt')) or \
               os.path.exists(os.path.join(project_path, 'pyproject.toml')) or \
               os.path.exists(os.path.join(project_path, 'setup.py')):
                project_info['type'] = 'python'
                project_info['language'] = 'python'
                
                # 检测Python框架
                frameworks = await self._detect_python_frameworks(project_path)
                project_info['framework'] = frameworks
                
            elif os.path.exists(os.path.join(project_path, 'package.json')):
                project_info['type'] = 'nodejs'
                project_info['language'] = 'javascript'
                
                # 检测Node.js框架
                frameworks = await self._detect_nodejs_frameworks(project_path)
                project_info['framework'] = frameworks
                
            elif os.path.exists(os.path.join(project_path, 'go.mod')):
                project_info['type'] = 'go'
                project_info['language'] = 'go'
                
            elif os.path.exists(os.path.join(project_path, 'Cargo.toml')):
                project_info['type'] = 'rust'
                project_info['language'] = 'rust'
                
            return project_info
            
        except Exception as e:
            logger.error(f"收集项目基础信息失败: {e}")
            return {'error': str(e)}
    
    async def _collect_directory_structure(self, project_path: str, max_depth: int = 3) -> Dict[str, Any]:
        """收集目录结构信息"""
        try:
            structure = {
                'root_files': [],
                'directories': {},
                'key_files': [],
                'total_files': 0,
                'total_dirs': 0
            }
            
            # 获取根目录文件
            for item in os.listdir(project_path):
                item_path = os.path.join(project_path, item)
                if os.path.isfile(item_path):
                    structure['root_files'].append(item)
                    structure['total_files'] += 1
                    
                    # 标记关键文件
                    if item in ['requirements.txt', 'package.json', 'Dockerfile', '.gitlab-ci.yml', 
                               'pyproject.toml', 'setup.py', '.flake8', 'tox.ini']:
                        structure['key_files'].append(item)
                        
                elif os.path.isdir(item_path) and not item.startswith('.'):
                    structure['total_dirs'] += 1
                    if max_depth > 0:
                        subdir_info = await self._get_directory_info(item_path, max_depth - 1)
                        structure['directories'][item] = subdir_info
            
            return structure
            
        except Exception as e:
            logger.error(f"收集目录结构失败: {e}")
            return {'error': str(e)}
    
    async def _collect_environment_info(self, project_path: str) -> Dict[str, Any]:
        """收集运行环境信息"""
        try:
            env_info = {
                'os': {
                    'system': platform.system(),
                    'release': platform.release(),
                    'version': platform.version(),
                    'machine': platform.machine(),
                    'processor': platform.processor()
                },
                'python': {
                    'version': sys.version,
                    'executable': sys.executable,
                    'path': sys.path[:5]  # 只取前5个路径
                },
                'working_directory': os.getcwd(),
                'environment_variables': {}
            }
            
            # 收集关键环境变量
            key_env_vars = [
                'PATH', 'PYTHONPATH', 'VIRTUAL_ENV', 'CONDA_DEFAULT_ENV',
                'CI', 'GITLAB_CI', 'GITHUB_ACTIONS',
                'NODE_ENV', 'ENVIRONMENT'
            ]
            
            for var in key_env_vars:
                if var in os.environ:
                    env_info['environment_variables'][var] = os.environ[var]
            
            # 检测虚拟环境
            if 'VIRTUAL_ENV' in os.environ:
                env_info['virtual_env'] = {
                    'active': True,
                    'path': os.environ['VIRTUAL_ENV'],
                    'type': 'venv'
                }
            elif 'CONDA_DEFAULT_ENV' in os.environ:
                env_info['virtual_env'] = {
                    'active': True,
                    'name': os.environ['CONDA_DEFAULT_ENV'],
                    'type': 'conda'
                }
            else:
                env_info['virtual_env'] = {'active': False}
            
            return env_info
            
        except Exception as e:
            logger.error(f"收集环境信息失败: {e}")
            return {'error': str(e)}
    
    async def _collect_config_files(self, project_path: str) -> Dict[str, Any]:
        """收集配置文件内容"""
        try:
            config_files = {}
            
            # 定义需要收集的配置文件
            config_file_patterns = {
                'ci_cd': ['.gitlab-ci.yml', '.github/workflows/*.yml', 'Jenkinsfile'],
                'python': ['.flake8', 'pyproject.toml', 'setup.cfg', 'tox.ini', 'pytest.ini'],
                'javascript': ['.eslintrc*', '.prettierrc*', 'jest.config.*'],
                'docker': ['Dockerfile', 'docker-compose.yml', '.dockerignore'],
                'dependencies': ['requirements.txt', 'package.json', 'go.mod', 'Cargo.toml']
            }
            
            for category, patterns in config_file_patterns.items():
                config_files[category] = {}
                for pattern in patterns:
                    files = await self._find_files_by_pattern(project_path, pattern)
                    for file_path in files:
                        try:
                            with open(file_path, 'r', encoding='utf-8') as f:
                                content = f.read()
                                # 限制内容长度
                                if len(content) > 2000:
                                    content = content[:2000] + "\n... (内容已截断)"
                                config_files[category][os.path.basename(file_path)] = content
                        except Exception as e:
                            config_files[category][os.path.basename(file_path)] = f"读取失败: {e}"
            
            return config_files
            
        except Exception as e:
            logger.error(f"收集配置文件失败: {e}")
            return {'error': str(e)}
    
    async def _collect_dependencies(self, project_path: str) -> Dict[str, Any]:
        """收集依赖信息"""
        try:
            dependencies = {}
            
            # Python依赖
            if os.path.exists(os.path.join(project_path, 'requirements.txt')):
                try:
                    result = subprocess.run(['pip', 'list'], capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        dependencies['python_packages'] = result.stdout
                except Exception as e:
                    dependencies['python_packages'] = f"获取失败: {e}"
            
            # Node.js依赖
            if os.path.exists(os.path.join(project_path, 'package.json')):
                try:
                    result = subprocess.run(['npm', 'list', '--depth=0'], 
                                          capture_output=True, text=True, timeout=10, cwd=project_path)
                    dependencies['npm_packages'] = result.stdout
                except Exception as e:
                    dependencies['npm_packages'] = f"获取失败: {e}"
            
            return dependencies
            
        except Exception as e:
            logger.error(f"收集依赖信息失败: {e}")
            return {'error': str(e)}
    
    async def _collect_git_info(self, project_path: str) -> Dict[str, Any]:
        """收集Git信息"""
        try:
            git_info = {}
            
            try:
                # 当前分支
                result = subprocess.run(['git', 'branch', '--show-current'], 
                                      capture_output=True, text=True, timeout=5, cwd=project_path)
                if result.returncode == 0:
                    git_info['current_branch'] = result.stdout.strip()
                
                # 最近提交
                result = subprocess.run(['git', 'log', '--oneline', '-5'], 
                                      capture_output=True, text=True, timeout=5, cwd=project_path)
                if result.returncode == 0:
                    git_info['recent_commits'] = result.stdout.strip()
                
                # 工作区状态
                result = subprocess.run(['git', 'status', '--porcelain'], 
                                      capture_output=True, text=True, timeout=5, cwd=project_path)
                if result.returncode == 0:
                    git_info['working_tree_status'] = result.stdout.strip()
                    
            except Exception as e:
                git_info['error'] = str(e)
            
            return git_info
            
        except Exception as e:
            logger.error(f"收集Git信息失败: {e}")
            return {'error': str(e)}
    
    def format_context_for_ai(self, context: Dict[str, Any], error_info: Dict[str, Any] = None) -> str:
        """
        将上下文信息格式化为AI友好的文本
        
        Args:
            context: 项目上下文信息
            error_info: 错误信息
            
        Returns:
            格式化的上下文文本
        """
        try:
            formatted_text = []
            
            # 项目基础信息
            if 'project_info' in context:
                project_info = context['project_info']
                formatted_text.append("## 📋 项目信息")
                formatted_text.append(f"- **项目名称**: {project_info.get('name', 'unknown')}")
                formatted_text.append(f"- **项目类型**: {project_info.get('type', 'unknown')}")
                formatted_text.append(f"- **编程语言**: {project_info.get('language', 'unknown')}")
                if project_info.get('framework'):
                    formatted_text.append(f"- **使用框架**: {', '.join(project_info['framework'])}")
                formatted_text.append("")
            
            # 运行环境信息
            if 'environment_info' in context:
                env_info = context['environment_info']
                formatted_text.append("## 🖥️ 运行环境")
                formatted_text.append(f"- **操作系统**: {env_info.get('os', {}).get('system', 'unknown')}")
                formatted_text.append(f"- **Python版本**: {env_info.get('python', {}).get('version', 'unknown')}")
                
                venv_info = env_info.get('virtual_env', {})
                if venv_info.get('active'):
                    formatted_text.append(f"- **虚拟环境**: {venv_info.get('type', 'unknown')} ({venv_info.get('name', venv_info.get('path', ''))})")
                else:
                    formatted_text.append("- **虚拟环境**: 未激活")
                formatted_text.append("")
            
            # 目录结构
            if 'directory_structure' in context:
                dir_info = context['directory_structure']
                formatted_text.append("## 📁 项目结构")
                formatted_text.append(f"- **根目录文件**: {', '.join(dir_info.get('root_files', [])[:10])}")
                if dir_info.get('key_files'):
                    formatted_text.append(f"- **关键配置文件**: {', '.join(dir_info['key_files'])}")
                formatted_text.append(f"- **目录数量**: {dir_info.get('total_dirs', 0)}")
                formatted_text.append(f"- **文件数量**: {dir_info.get('total_files', 0)}")
                formatted_text.append("")
            
            # Git信息
            if 'git_info' in context and context['git_info']:
                git_info = context['git_info']
                formatted_text.append("## 🔄 Git信息")
                if 'current_branch' in git_info:
                    formatted_text.append(f"- **当前分支**: {git_info['current_branch']}")
                if 'recent_commits' in git_info:
                    formatted_text.append("- **最近提交**:")
                    for line in git_info['recent_commits'].split('\n')[:3]:
                        formatted_text.append(f"  - {line}")
                formatted_text.append("")
            
            # 错误上下文
            if error_info:
                formatted_text.append("## ⚠️ 错误上下文")
                if 'command' in error_info:
                    formatted_text.append(f"- **失败命令**: {error_info['command']}")
                if 'working_directory' in error_info:
                    formatted_text.append(f"- **工作目录**: {error_info['working_directory']}")
                if 'error_message' in error_info:
                    formatted_text.append(f"- **错误信息**: {error_info['error_message']}")
                formatted_text.append("")
            
            return '\n'.join(formatted_text)
            
        except Exception as e:
            logger.error(f"格式化上下文失败: {e}")
            return f"上下文格式化失败: {str(e)}"
    
    # 辅助方法
    def _get_timestamp(self) -> str:
        """获取时间戳"""
        from datetime import datetime
        return datetime.now().isoformat()
    
    async def _detect_python_frameworks(self, project_path: str) -> List[str]:
        """检测Python框架"""
        frameworks = []
        
        # 检查requirements.txt或已安装包
        try:
            result = subprocess.run(['pip', 'list'], capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                packages = result.stdout.lower()
                if 'fastapi' in packages:
                    frameworks.append('FastAPI')
                if 'django' in packages:
                    frameworks.append('Django')
                if 'flask' in packages:
                    frameworks.append('Flask')
                if 'pytest' in packages:
                    frameworks.append('pytest')
        except:
            pass
        
        return frameworks
    
    async def _detect_nodejs_frameworks(self, project_path: str) -> List[str]:
        """检测Node.js框架"""
        frameworks = []
        
        package_json_path = os.path.join(project_path, 'package.json')
        if os.path.exists(package_json_path):
            try:
                with open(package_json_path, 'r') as f:
                    package_data = json.load(f)
                    
                dependencies = {**package_data.get('dependencies', {}), 
                              **package_data.get('devDependencies', {})}
                
                if 'react' in dependencies:
                    frameworks.append('React')
                if 'vue' in dependencies:
                    frameworks.append('Vue')
                if 'express' in dependencies:
                    frameworks.append('Express')
                if 'jest' in dependencies:
                    frameworks.append('Jest')
            except:
                pass
        
        return frameworks
    
    async def _get_directory_info(self, dir_path: str, max_depth: int) -> Dict[str, Any]:
        """获取目录信息"""
        info = {'files': [], 'subdirs': {}}
        
        try:
            for item in os.listdir(dir_path):
                item_path = os.path.join(dir_path, item)
                if os.path.isfile(item_path):
                    info['files'].append(item)
                elif os.path.isdir(item_path) and not item.startswith('.') and max_depth > 0:
                    info['subdirs'][item] = await self._get_directory_info(item_path, max_depth - 1)
        except:
            pass
        
        return info
    
    async def _find_files_by_pattern(self, project_path: str, pattern: str) -> List[str]:
        """根据模式查找文件"""
        import glob
        
        files = []
        try:
            if '*' in pattern:
                files = glob.glob(os.path.join(project_path, pattern), recursive=True)
            else:
                file_path = os.path.join(project_path, pattern)
                if os.path.exists(file_path):
                    files = [file_path]
        except:
            pass
        
        return files


# 全局实例
project_context_collector = ProjectContextCollector()
