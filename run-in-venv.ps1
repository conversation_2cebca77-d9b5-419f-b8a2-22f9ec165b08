# 设置环境变量，确保Python使用UTF-8编码
$env:PYTHONIOENCODING = "utf-8"
# Set environment variables
$env:HOST = "0.0.0.0"
$env:PORT = "8000"
$env:SERVICE_ADDRESS = "**************"
$env:EXTERNAL_PORT = "8000"
$env:CONSUL_HOST="***************"
$env:CONSUL_PORT="8500"
$env:REDIS_HOST="***************"
$env:REDIS_PORT="6379"
$env:GITLAB_WEBHOOK_TOKEN = "test-webhook-token"
$env:GITLAB_WEBHOOK_ALLOWED_IPS = "***************"
$env:BOT_NAMES = "aider-bot"
$env:LOG_LEVEL = "DEBUG"
$env:JWT_SECRET_KEY = "local-test-secret-key"
$env:VALID_API_KEYS = "test-api-key"
$env:ALLOWED_USERS = "Longer"
$env:GITLAB_API_URL = "http://***************/api/v4"
$env:GITLAB_API_TOKEN = "**************************"
# $env:GITLAB_DEFAULT_PROJECT_ID = "3"  # 已禁用：不设置默认项目，强制要求明确的项目ID

# OpenRouter API 配置
#$env:OPENROUTER_API_KEY = "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"  # 您的 OpenRouter API 密钥
#$env:OPENROUTER_API_KEY = "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"  # 您的 OpenRouter API 密钥
$env:OPENROUTER_API_KEY = "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"  # 您的 OpenRouter API 密钥
#$env:OPENROUTER_API_KEY = "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"  # 您的 OpenRouter API 密钥

$env:AIDER_MODEL = "openrouter/deepseek/deepseek-chat-v3-0324:free"  # 使用 DeepSeek Chat 模型
$env:ANALYSIS_MODEL= "openrouter/deepseek/deepseek-r1:free"
$env:AIDER_CHAT_LANGUAGE = "Chinese"  # 设置AI默认使用中文回复
$env:GIT_USER_NAME="aider-worker"
$env:GIT_USER_EMAIL="<EMAIL>"

# 设置项目下载目录 - 使用固定路径
$projectsDir = "E:\aider-git-repos\"
if (-not (Test-Path $projectsDir)) {
    Write-Host "Creating git repositories directory: $projectsDir" -ForegroundColor Cyan
    New-Item -Path $projectsDir -ItemType Directory -Force | Out-Null
}
$env:PROJECTS_DIR = $projectsDir
Write-Host "Git repositories will be downloaded to: $projectsDir" -ForegroundColor Yellow

Write-Host "Service will run at $($env:SERVICE_ADDRESS):$($env:EXTERNAL_PORT)" -ForegroundColor Yellow
Write-Host "GitLab Webhook URL: http://$($env:SERVICE_ADDRESS):$($env:EXTERNAL_PORT)/webhook/gitlab/" -ForegroundColor Yellow
Write-Host "GitLab Webhook Token: $($env:GITLAB_WEBHOOK_TOKEN)" -ForegroundColor Yellow
Write-Host "GitLab API URL: $($env:GITLAB_API_URL)" -ForegroundColor Yellow
Write-Host "GitLab API Token: $($env:GITLAB_API_TOKEN.Substring(0, 5))..." -ForegroundColor Yellow

# 设置Python路径，确保能找到bot_agent目录
$env:PYTHONPATH = "$PWD"
Write-Host "Setting PYTHONPATH to: $env:PYTHONPATH" -ForegroundColor Cyan

# 确保bot_agent目录被识别为Python模块
if (-not (Test-Path "bot_agent/__init__.py")) {
    Write-Host "Creating __init__.py in bot_agent directory..." -ForegroundColor Cyan
    New-Item -Path "bot_agent/__init__.py" -ItemType File -Force | Out-Null
}



# 检查是否安装了vim
$vimInstalled = $null
try {
    $vimInstalled = Get-Command vim -ErrorAction SilentlyContinue
} catch {
    $vimInstalled = $null
}

if ($vimInstalled) {
    Write-Host "Vim is installed and available for Augment integration." -ForegroundColor Green

    # 检查Vim版本
    $vimVersion = vim --version | Select-String -Pattern "VIM - Vi IMproved (\d+)\.(\d+)" | ForEach-Object { $_.Matches.Groups[1].Value + "." + $_.Matches.Groups[2].Value }
    Write-Host "Detected Vim version: $vimVersion" -ForegroundColor Cyan

    # 检查是否满足版本要求
    $vimMajor = [int]($vimVersion -split '\.')[0]
    $vimMinor = [int]($vimVersion -split '\.')[1]

    if ($vimMajor -gt 9 -or ($vimMajor -eq 9 -and $vimMinor -ge 1)) {
        Write-Host "Vim version meets requirements (9.1.0 or higher)." -ForegroundColor Green
    } else {
        Write-Host "Warning: Vim version is below recommended version (9.1.0)." -ForegroundColor Yellow
        Write-Host "Some Augment features may not work properly." -ForegroundColor Yellow
    }
} else {
    Write-Host "Warning: Vim is not installed or not in PATH. Augment Vim integration may not work properly." -ForegroundColor Yellow
    Write-Host "Please install Vim 9.1.0 or higher to use all Augment features." -ForegroundColor Yellow
}

# 检查是否安装了Node.js
$nodeInstalled = $null
try {
    $nodeInstalled = Get-Command node -ErrorAction SilentlyContinue
} catch {
    $nodeInstalled = $null
}

if ($nodeInstalled) {
    Write-Host "Node.js is installed and available for Augment integration." -ForegroundColor Green

    # 检查Node.js版本
    $nodeVersion = node --version
    Write-Host "Detected Node.js version: $nodeVersion" -ForegroundColor Cyan

    # 检查是否满足版本要求
    $nodeMajor = [int]($nodeVersion -replace 'v', '' -split '\.')[0]

    if ($nodeMajor -ge 22) {
        Write-Host "Node.js version meets requirements (22.0.0 or higher)." -ForegroundColor Green
    } else {
        Write-Host "Warning: Node.js version is below recommended version (22.0.0)." -ForegroundColor Yellow
        Write-Host "Some Augment features may not work properly." -ForegroundColor Yellow
    }
} else {
    Write-Host "Warning: Node.js is not installed or not in PATH." -ForegroundColor Yellow
    Write-Host "Please install Node.js 22.0.0 or higher for better functionality." -ForegroundColor Yellow
}

# Vim/Neovim 插件检查已移除，专注于 Aider 功能

# 确保所有必要的依赖都已安装
Write-Host "Checking and installing required dependencies..." -ForegroundColor Cyan
# 使用-E参数指定UTF-8编码
$env:PIP_NO_CACHE_DIR = "off"
$env:PIP_DISABLE_PIP_VERSION_CHECK = "on"
# 使用Python直接调用pip，并使用UTF-8编码的requirements文件
python -m pip install -r requirements.txt --no-cache-dir

# 检查是否启用热重载
$hot_reload = $args -contains "--hot-reload"

# Start the service
Write-Host "Starting Bot Agent service..." -ForegroundColor Cyan

if ($hot_reload) {
    # 使用uvicorn直接启动，启用热重载
    Write-Host "Running with hot reload enabled..." -ForegroundColor Green
    Write-Host "Service will be available at http://localhost:8000" -ForegroundColor Green
    Write-Host "Press Ctrl+C to stop the service" -ForegroundColor Yellow

    # 监视的目录
    $watch_dirs = "bot_agent,aider"

    # 启动uvicorn，启用热重载
    uvicorn bot_agent.api.app:app --reload --host 0.0.0.0 --port 8000 --reload-dir bot_agent --reload-dir aider
} else {
    # 使用Python模块方式运行
    Write-Host "Running: python -m bot_agent.main" -ForegroundColor Cyan
    Write-Host "Service will be available at http://localhost:8000" -ForegroundColor Green
    Write-Host "Press Ctrl+C to stop the service" -ForegroundColor Yellow
    Write-Host "Tip: Run with --hot-reload flag to enable hot reloading" -ForegroundColor Cyan

    # 使用-m参数以模块方式运行
    python -m bot_agent.main
}


