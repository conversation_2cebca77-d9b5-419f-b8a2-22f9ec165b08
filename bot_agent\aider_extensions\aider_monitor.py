#!/usr/bin/env python3
"""
Aider操作监控和代理系统
让所有Aider操作透明化、可监控、可控制
"""

import os
import sys
import time
import logging
import subprocess
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass
from datetime import datetime

logger = logging.getLogger(__name__)


@dataclass
class AiderOperation:
    """Aider操作记录"""
    operation_type: str  # 'file_edit', 'git_commit', 'shell_command', 'tool_install'
    timestamp: datetime
    description: str
    details: Dict[str, Any]
    success: bool = True
    error_message: str = ""
    duration: float = 0.0


class AiderMonitor:
    """Aider操作监控器"""
    
    def __init__(self):
        self.operations: List[AiderOperation] = []
        self.start_time = time.time()
        self.callbacks: List[Callable] = []
    
    def add_callback(self, callback: Callable):
        """添加操作回调"""
        self.callbacks.append(callback)
    
    def log_operation(self, operation: AiderOperation):
        """记录操作"""
        self.operations.append(operation)
        
        # 输出到日志
        status = "✅" if operation.success else "❌"
        logger.info(f"[AIDER_MONITOR] {status} {operation.operation_type}: {operation.description}")
        
        if operation.details:
            for key, value in operation.details.items():
                logger.info(f"[AIDER_MONITOR]   {key}: {value}")
        
        if operation.error_message:
            logger.error(f"[AIDER_MONITOR]   错误: {operation.error_message}")
        
        # 调用回调函数
        for callback in self.callbacks:
            try:
                callback(operation)
            except Exception as e:
                logger.error(f"监控回调失败: {e}")
    
    def get_summary(self) -> Dict[str, Any]:
        """获取操作摘要"""
        total_operations = len(self.operations)
        successful_operations = sum(1 for op in self.operations if op.success)
        failed_operations = total_operations - successful_operations
        
        operation_types = {}
        for op in self.operations:
            operation_types[op.operation_type] = operation_types.get(op.operation_type, 0) + 1
        
        total_duration = time.time() - self.start_time
        
        return {
            'total_operations': total_operations,
            'successful_operations': successful_operations,
            'failed_operations': failed_operations,
            'operation_types': operation_types,
            'total_duration': total_duration,
            'operations': self.operations
        }


class MonitoredIO:
    """监控的IO类，代理Aider的IO操作"""
    
    def __init__(self, original_io, monitor: AiderMonitor):
        self.original_io = original_io
        self.monitor = monitor
        
        # 代理所有原始IO的属性和方法
        for attr in dir(original_io):
            if not attr.startswith('_') and not hasattr(self, attr):
                setattr(self, attr, getattr(original_io, attr))
    
    def tool_output(self, message: str):
        """监控工具输出"""
        self.monitor.log_operation(AiderOperation(
            operation_type='tool_output',
            timestamp=datetime.now(),
            description='工具输出',
            details={'message': message}
        ))
        
        # 检查是否是安装操作
        if 'Installing' in message or 'pip install' in message:
            self.monitor.log_operation(AiderOperation(
                operation_type='tool_install',
                timestamp=datetime.now(),
                description='检测到工具安装',
                details={'install_message': message}
            ))
        
        return self.original_io.tool_output(message)
    
    def tool_error(self, message: str):
        """监控工具错误"""
        self.monitor.log_operation(AiderOperation(
            operation_type='tool_error',
            timestamp=datetime.now(),
            description='工具错误',
            details={'error_message': message},
            success=False,
            error_message=message
        ))
        
        return self.original_io.tool_error(message)
    
    def confirm_ask(self, question: str, default: bool = True) -> bool:
        """监控确认询问"""
        self.monitor.log_operation(AiderOperation(
            operation_type='user_confirm',
            timestamp=datetime.now(),
            description='用户确认询问',
            details={'question': question, 'default': default}
        ))
        
        # 自动确认，避免卡住
        result = True  # 或者调用 self.original_io.confirm_ask(question, default)
        
        self.monitor.log_operation(AiderOperation(
            operation_type='user_confirm_result',
            timestamp=datetime.now(),
            description='用户确认结果',
            details={'question': question, 'result': result}
        ))
        
        return result


class MonitoredCoder:
    """监控的Coder类，代理Aider的Coder操作"""
    
    def __init__(self, original_coder, monitor: AiderMonitor):
        self.original_coder = original_coder
        self.monitor = monitor
        
        # 代理所有原始Coder的属性和方法
        for attr in dir(original_coder):
            if not attr.startswith('_') and not hasattr(self, attr):
                setattr(self, attr, getattr(original_coder, attr))
    
    def run(self, with_message: str = None, **kwargs):
        """监控Coder运行"""
        start_time = time.time()
        
        self.monitor.log_operation(AiderOperation(
            operation_type='coder_run_start',
            timestamp=datetime.now(),
            description='开始Coder执行',
            details={
                'message': with_message[:200] + '...' if with_message and len(with_message) > 200 else with_message,
                'kwargs': str(kwargs)
            }
        ))
        
        try:
            result = self.original_coder.run(with_message=with_message, **kwargs)
            
            duration = time.time() - start_time
            self.monitor.log_operation(AiderOperation(
                operation_type='coder_run_complete',
                timestamp=datetime.now(),
                description='Coder执行完成',
                details={
                    'result_length': len(result) if result else 0,
                    'result_preview': result[:200] + '...' if result and len(result) > 200 else result
                },
                duration=duration
            ))
            
            return result
            
        except Exception as e:
            duration = time.time() - start_time
            self.monitor.log_operation(AiderOperation(
                operation_type='coder_run_error',
                timestamp=datetime.now(),
                description='Coder执行失败',
                details={'error': str(e)},
                success=False,
                error_message=str(e),
                duration=duration
            ))
            raise
    
    def apply_edits(self, edits):
        """监控编辑应用"""
        self.monitor.log_operation(AiderOperation(
            operation_type='apply_edits_start',
            timestamp=datetime.now(),
            description='开始应用编辑',
            details={'edit_count': len(edits) if edits else 0}
        ))
        
        try:
            result = self.original_coder.apply_edits(edits)
            
            self.monitor.log_operation(AiderOperation(
                operation_type='apply_edits_complete',
                timestamp=datetime.now(),
                description='编辑应用完成',
                details={'edit_count': len(edits) if edits else 0}
            ))
            
            return result
            
        except Exception as e:
            self.monitor.log_operation(AiderOperation(
                operation_type='apply_edits_error',
                timestamp=datetime.now(),
                description='编辑应用失败',
                details={'error': str(e)},
                success=False,
                error_message=str(e)
            ))
            raise


class AiderProxy:
    """Aider代理类，提供完全透明的Aider操作"""
    
    def __init__(self):
        self.monitor = AiderMonitor()
        self.setup_monitoring_callbacks()
    
    def setup_monitoring_callbacks(self):
        """设置监控回调"""
        def operation_callback(operation: AiderOperation):
            # 可以在这里添加特殊处理逻辑
            if operation.operation_type == 'tool_install':
                logger.warning(f"🚨 检测到工具安装: {operation.description}")
            elif operation.operation_type == 'coder_run_error':
                logger.error(f"🚨 Coder执行失败: {operation.error_message}")
        
        self.monitor.add_callback(operation_callback)
    
    def create_monitored_coder(self, **kwargs):
        """创建监控的Coder"""
        try:
            from aider.coders import Coder
            from aider.io import InputOutput
            
            # 创建监控的IO
            original_io = kwargs.get('io') or InputOutput(pretty=False, yes=True)
            monitored_io = MonitoredIO(original_io, self.monitor)
            kwargs['io'] = monitored_io
            
            # 创建原始Coder
            original_coder = Coder.create(**kwargs)
            
            # 创建监控的Coder
            monitored_coder = MonitoredCoder(original_coder, self.monitor)
            
            self.monitor.log_operation(AiderOperation(
                operation_type='coder_created',
                timestamp=datetime.now(),
                description='创建监控的Coder',
                details={'kwargs': str(kwargs)}
            ))
            
            return monitored_coder
            
        except Exception as e:
            self.monitor.log_operation(AiderOperation(
                operation_type='coder_creation_error',
                timestamp=datetime.now(),
                description='创建Coder失败',
                details={'error': str(e)},
                success=False,
                error_message=str(e)
            ))
            raise
    
    def get_operation_summary(self) -> str:
        """获取操作摘要报告"""
        summary = self.monitor.get_summary()
        
        report = f"""
## 🔍 Aider操作监控报告

### 📊 总体统计
- **总操作数**: {summary['total_operations']}
- **成功操作**: {summary['successful_operations']}
- **失败操作**: {summary['failed_operations']}
- **总耗时**: {summary['total_duration']:.2f}秒

### 📋 操作类型分布
"""
        
        for op_type, count in summary['operation_types'].items():
            report += f"- **{op_type}**: {count}次\n"
        
        report += "\n### 📝 详细操作记录\n"
        
        for i, op in enumerate(summary['operations'][-10:], 1):  # 显示最后10个操作
            status = "✅" if op.success else "❌"
            report += f"{i}. {status} **{op.operation_type}**: {op.description}\n"
            if op.error_message:
                report += f"   错误: {op.error_message}\n"
        
        return report


# 全局代理实例
aider_proxy = AiderProxy()
