#!/usr/bin/env python3
"""
测试项目上下文收集功能集成
"""

import sys
import os
import asyncio

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_context_collection():
    """测试上下文收集功能"""
    print("🔍 测试项目上下文收集功能...")
    
    try:
        from bot_agent.tools.project_context_collector import project_context_collector
        
        # 测试项目路径
        test_project_path = os.getcwd()
        print(f"📁 测试项目路径: {test_project_path}")
        
        # 模拟错误信息
        mock_error_info = {
            'errors': [
                'ValueError: Error code \'#\' supplied to \'extend-ignore\' option',
                'flake8: configuration error in .flake8',
                'lint check failed'
            ]
        }
        
        # 收集项目上下文
        print("📋 开始收集项目上下文...")
        context = await project_context_collector.collect_full_context(
            test_project_path, 
            mock_error_info
        )
        
        print("✅ 上下文收集完成")
        print(f"📊 收集到的上下文维度: {list(context.keys())}")
        
        # 测试上下文格式化
        print("\n🎨 测试上下文格式化...")
        formatted_context = project_context_collector.format_context_for_ai(
            context, 
            mock_error_info
        )
        
        print("✅ 上下文格式化完成")
        print(f"📝 格式化后长度: {len(formatted_context)} 字符")
        
        # 显示格式化结果的前500字符
        print("\n📄 格式化结果预览:")
        print("-" * 50)
        print(formatted_context[:500])
        if len(formatted_context) > 500:
            print("... (内容已截断)")
        print("-" * 50)
        
        return True
        
    except Exception as e:
        print(f"❌ 上下文收集测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_ai_integration():
    """测试AI集成功能"""
    print("\n🤖 测试AI集成功能...")
    
    try:
        from bot_agent.tools.intelligent_tool_coordinator import IntelligentToolCoordinator
        
        coordinator = IntelligentToolCoordinator()
        print("✅ 智能工具协调器初始化成功")
        
        # 模拟错误列表
        mock_errors = [
            'ValueError: Error code \'#\' supplied to \'extend-ignore\' option does not match \'^[A-Z]{1,3}[0-9]{0,3}$\'',
            'flake8: configuration error in .flake8'
        ]
        
        test_project_path = os.getcwd()
        
        print("🔧 测试AI修复方案生成...")
        
        # 测试AI生成修复方案（不实际调用API，只测试逻辑）
        print("📝 模拟AI修复方案生成流程...")
        
        # 检查方法是否存在
        if hasattr(coordinator, '_ai_generate_fix_plan'):
            print("✅ _ai_generate_fix_plan 方法存在")
        else:
            print("❌ _ai_generate_fix_plan 方法不存在")
            return False
        
        if hasattr(coordinator, '_format_errors_for_ai'):
            print("✅ _format_errors_for_ai 方法存在")
        else:
            print("❌ _format_errors_for_ai 方法不存在")
            return False
        
        # 测试错误格式化
        formatted_errors = coordinator._format_errors_for_ai(mock_errors)
        print(f"✅ 错误格式化成功，长度: {len(formatted_errors)} 字符")
        
        print("🎉 AI集成功能测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ AI集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_context_format_quality():
    """测试上下文格式化质量"""
    print("\n📊 测试上下文格式化质量...")
    
    try:
        from bot_agent.tools.project_context_collector import project_context_collector
        
        # 模拟完整的上下文数据
        mock_context = {
            'project_type': {
                'name': 'aider-plus',
                'type': 'python',
                'language': 'python'
            },
            'environment_info': {
                'success': True,
                'data': {
                    'system': 'Windows',
                    'python_version': '3.9.0',
                    'platform': 'Windows-10-10.0.19041-SP0'
                }
            },
            'project_structure': {
                'summary': '项目包含 150 个文件，25 个目录'
            },
            'configurations': {
                'summary': '找到 5 个配置文件',
                'configs': [
                    {'file': '.flake8', 'description': 'Python代码质量配置'},
                    {'file': 'pyproject.toml', 'description': 'Python项目配置'},
                    {'file': '.gitlab-ci.yml', 'description': 'GitLab CI/CD配置'}
                ]
            },
            'git_info': {
                'branch': 'main',
                'recent_commits': ['abc123 Fix flake8 config', 'def456 Add new feature']
            }
        }
        
        mock_error_info = {
            'errors': [
                'flake8 configuration error',
                'lint check failed',
                'syntax error in config file'
            ]
        }
        
        # 测试格式化
        formatted = project_context_collector.format_context_for_ai(mock_context, mock_error_info)
        
        # 检查格式化质量
        quality_checks = [
            ("包含项目信息", "## 📋 项目信息" in formatted),
            ("包含运行环境", "## 🖥️ 运行环境" in formatted),
            ("包含项目结构", "## 📁 项目结构" in formatted),
            ("包含配置文件", "## ⚙️ 配置文件" in formatted),
            ("包含Git信息", "## 🔄 Git信息" in formatted),
            ("包含错误上下文", "## ⚠️ 错误上下文" in formatted),
            ("格式化长度合理", 100 < len(formatted) < 2000),
            ("包含项目名称", "aider-plus" in formatted),
            ("包含操作系统", "Windows" in formatted),
            ("包含Python版本", "3.9.0" in formatted)
        ]
        
        passed_checks = 0
        total_checks = len(quality_checks)
        
        print("🔍 质量检查结果:")
        for check_name, result in quality_checks:
            status = "✅" if result else "❌"
            print(f"  {status} {check_name}")
            if result:
                passed_checks += 1
        
        print(f"\n📊 质量评分: {passed_checks}/{total_checks} ({passed_checks/total_checks*100:.1f}%)")
        
        if passed_checks >= total_checks * 0.8:  # 80%通过率
            print("🎉 上下文格式化质量测试通过！")
            return True
        else:
            print("⚠️ 上下文格式化质量需要改进")
            return False
        
    except Exception as e:
        print(f"❌ 上下文格式化质量测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主测试函数"""
    print("🧪 项目上下文收集功能集成测试")
    print("=" * 60)
    
    tests = [
        ("项目上下文收集", test_context_collection),
        ("AI集成功能", test_ai_integration),
        ("上下文格式化质量", test_context_format_quality),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔬 执行 {test_name}测试...")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            import traceback
            traceback.print_exc()
            results.append((test_name, False))
    
    # 汇总结果
    print("\n📊 测试结果汇总:")
    print("-" * 40)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！项目上下文收集功能已成功集成。")
        print("\n💡 集成完成的功能：")
        print("1. ✅ 复用现有工具收集项目信息")
        print("2. ✅ 智能格式化上下文为AI友好格式")
        print("3. ✅ 集成到AI修复方案生成流程")
        print("4. ✅ 提供丰富的项目背景信息")
        print("5. ✅ 支持多种项目类型检测")
        print("\n🎯 解决的问题：")
        print("- 🔴 AI缺乏项目背景 → ✅ 提供完整项目上下文")
        print("- 🔴 修复命令不适配环境 → ✅ 基于环境生成命令")
        print("- 🔴 忽略项目特性 → ✅ 考虑项目类型和配置")
        print("- 🔴 缺乏智能决策依据 → ✅ 丰富的上下文信息")
        print("\n🚀 现在AI能够基于项目上下文生成更准确的修复方案！")
    else:
        print("⚠️ 部分测试失败，集成可能不完整。")
    
    return passed == total

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
