#!/usr/bin/env python3
"""
测试多轮交互修复功能
"""

import sys
import os
import tempfile

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_command_retry_mechanism():
    """测试命令重试机制"""
    print("🔄 测试命令重试机制...")
    
    try:
        from bot_agent.tools.intelligent_tool_coordinator import IntelligentToolCoordinator
        
        # 创建协调器
        coordinator = IntelligentToolCoordinator()
        
        # 创建临时项目目录
        with tempfile.TemporaryDirectory() as temp_dir:
            print(f"📝 创建测试项目: {temp_dir}")
            
            # 测试Linux命令在Windows环境下的自动替代
            import asyncio
            
            # 模拟一个会失败的Linux命令
            result = asyncio.run(coordinator._execute_command_with_retry(
                command="grep -r 'test' ./*",  # Linux命令，在Windows下会失败
                description="搜索文件中的test字符串",
                project_path=temp_dir,
                max_retries=2
            ))
            
            print(f"📊 执行结果: {result.get('success', False)}")
            print(f"📝 消息: {result.get('message', '')}")
            print(f"🔄 尝试次数: {result.get('final_attempt', 0)}")
            
            attempts = result.get('attempts', [])
            for i, attempt in enumerate(attempts, 1):
                success = attempt.get('success', False)
                command = attempt.get('command', '')
                error = attempt.get('error', '')
                
                status = "✅" if success else "❌"
                print(f"  {status} 尝试{i}: {command}")
                if error:
                    print(f"    错误: {error}")
            
            # 检查是否有AI生成的替代命令
            if len(attempts) > 1:
                first_command = attempts[0].get('command', '')
                second_command = attempts[1].get('command', '') if len(attempts) > 1 else ''
                
                if first_command != second_command:
                    print(f"✅ AI成功生成替代命令")
                    print(f"  原命令: {first_command}")
                    print(f"  替代命令: {second_command}")
                    return True
                else:
                    print("⚠️ AI未生成替代命令")
                    return False
            else:
                print("⚠️ 只执行了一次尝试")
                return False
        
    except Exception as e:
        print(f"❌ 命令重试机制测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ai_alternative_generation():
    """测试AI替代命令生成"""
    print("\n🤖 测试AI替代命令生成...")
    
    try:
        from bot_agent.tools.intelligent_tool_coordinator import IntelligentToolCoordinator
        
        # 创建协调器
        coordinator = IntelligentToolCoordinator()
        
        # 测试常见的跨平台命令替代
        test_cases = [
            {
                'original': 'grep -r "test" ./*',
                'error': 'grep : 无法将"grep"项识别为 cmdlet、函数、脚本文件或可运行程序的名称',
                'description': '搜索文件中的test字符串'
            },
            {
                'original': 'find . -name "*.py"',
                'error': 'find : 无法将"find"项识别为 cmdlet、函数、脚本文件或可运行程序的名称',
                'description': '查找Python文件'
            },
            {
                'original': 'cat requirements.txt',
                'error': 'cat : 无法将"cat"项识别为 cmdlet、函数、脚本文件或可运行程序的名称',
                'description': '查看requirements.txt文件内容'
            }
        ]
        
        import asyncio
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n🔬 测试案例 {i}: {test_case['original']}")
            
            alternative = asyncio.run(coordinator._get_ai_alternative_command(
                original_command=test_case['original'],
                error_message=test_case['error'],
                description=test_case['description'],
                project_path="."
            ))
            
            if alternative:
                print(f"✅ AI生成替代命令: {alternative}")
                
                # 验证替代命令是否合理
                if 'Select-String' in alternative or 'Get-ChildItem' in alternative or 'Get-Content' in alternative:
                    print(f"✅ 替代命令使用了正确的PowerShell cmdlet")
                else:
                    print(f"⚠️ 替代命令可能不是最佳选择")
            else:
                print(f"❌ AI未能生成替代命令")
        
        return True
        
    except Exception as e:
        print(f"❌ AI替代命令生成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_multi_round_fix_workflow():
    """测试完整的多轮修复工作流程"""
    print("\n🔄 测试完整的多轮修复工作流程...")
    
    try:
        from bot_agent.tools.intelligent_tool_coordinator import IntelligentToolCoordinator
        
        # 创建协调器
        coordinator = IntelligentToolCoordinator()
        
        # 模拟一个包含跨平台命令问题的修复步骤
        fix_step = {
            'step': 1,
            'description': '搜索项目中的配置错误',
            'command': 'grep -r "extend-ignore.*#" ./*',  # Linux命令
            'critical': False,
            'expected_result': '找到配置错误'
        }
        
        # 创建临时项目目录
        with tempfile.TemporaryDirectory() as temp_dir:
            print(f"📝 创建测试项目: {temp_dir}")
            
            # 执行AI修复步骤（应该会自动重试和替代）
            import asyncio
            result = asyncio.run(coordinator._execute_ai_fix_step(fix_step, temp_dir))
            
            print(f"📊 修复步骤结果: {result.get('success', False)}")
            print(f"📝 消息: {result.get('message', '')}")
            
            attempts = result.get('attempts', [])
            if attempts:
                print(f"🔄 执行了 {len(attempts)} 次尝试")
                
                for i, attempt in enumerate(attempts, 1):
                    success = attempt.get('success', False)
                    command = attempt.get('command', '')
                    
                    status = "✅" if success else "❌"
                    print(f"  {status} 尝试{i}: {command}")
                
                # 检查是否展示了多轮交互能力
                if len(attempts) > 1:
                    print("✅ 系统展示了多轮交互能力")
                    return True
                else:
                    print("⚠️ 只执行了一次尝试")
                    return False
            else:
                print("❌ 没有执行尝试记录")
                return False
        
    except Exception as e:
        print(f"❌ 多轮修复工作流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🧪 多轮交互修复功能测试")
    print("=" * 50)
    
    tests = [
        ("命令重试机制", test_command_retry_mechanism),
        ("AI替代命令生成", test_ai_alternative_generation),
        ("多轮修复工作流程", test_multi_round_fix_workflow),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔬 执行 {test_name}测试...")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            import traceback
            traceback.print_exc()
            results.append((test_name, False))
    
    # 汇总结果
    print("\n📊 测试结果汇总:")
    print("-" * 30)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！多轮交互修复功能完善。")
        print("\n💡 新功能优势：")
        print("1. ✅ 命令执行失败时自动重试")
        print("2. ✅ AI智能生成替代命令")
        print("3. ✅ 跨平台命令自动转换")
        print("4. ✅ 多轮交互直到成功")
        print("5. ✅ 详细的执行过程记录")
        print("\n🎯 解决的问题：")
        print("- 🔴 单次执行失败就结束 → ✅ 多轮重试机制")
        print("- 🔴 跨平台命令兼容性 → ✅ AI智能替代")
        print("- 🔴 错误无法反馈给AI → ✅ 实时错误反馈")
        print("- 🔴 缺乏自适应能力 → ✅ 智能命令调整")
        print("\n🚀 现在系统具备真正的自适应修复能力！")
    else:
        print("⚠️ 部分测试失败，功能可能不完整。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
