#!/usr/bin/env python3
"""
积极主动的Aider Coder扩展
修改Aider的保守行为，让它更加积极主动地执行任务
"""

import os
import sys
from typing import Optional, List, Dict, Any

# 添加aider到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'aider'))

try:
    from aider.coders.base_coder import Coder
    from aider.coders.architect_coder import ArchitectCoder
    from aider.coders.editblock_coder import EditBlockCoder
    from aider.io import InputOutput
    from aider.models import Model
except ImportError as e:
    print(f"无法导入Aider模块: {e}")
    # 创建占位符类
    class Coder:
        pass
    class ArchitectCoder:
        pass
    class EditBlockCoder:
        pass
    class InputOutput:
        pass
    class Model:
        pass


class AggressiveIO(InputOutput):
    """
    积极主动的IO类
    自动确认所有操作，不询问用户
    """
    
    def __init__(self, *args, **kwargs):
        # 强制设置为非交互模式
        kwargs['yes'] = True
        kwargs['pretty'] = False
        super().__init__(*args, **kwargs)
    
    def confirm_ask(self, question: str, default: bool = True) -> bool:
        """
        自动确认所有问题
        """
        print(f"🤖 自动确认: {question} -> {'是' if default else '否'}")
        return default
    
    def tool_error(self, message: str):
        """
        工具错误处理 - 不中断执行
        """
        print(f"⚠️ 工具警告: {message}")
        # 不抛出异常，继续执行
    
    def tool_output(self, message: str):
        """
        工具输出 - 简化显示
        """
        print(f"🔧 工具输出: {message}")


class AggressiveArchitectCoder(ArchitectCoder):
    """
    积极主动的架构师Coder
    自动接受所有编辑建议
    """
    
    def __init__(self, *args, **kwargs):
        # 强制启用自动接受
        kwargs['auto_accept_architect'] = True
        super().__init__(*args, **kwargs)
        
        # 设置更积极的参数
        self.auto_accept_architect = True
        self.suggest_shell_commands = True
        self.auto_lint = False  # 禁用自动lint以避免中断
        self.auto_test = False  # 禁用自动测试以避免中断
    
    def reply_completed(self):
        """
        回复完成后自动执行编辑
        """
        content = self.partial_response_content
        
        if not content or not content.strip():
            return
        
        print("🚀 自动执行编辑操作...")
        # 直接执行编辑，不询问确认
        try:
            # 调用父类的编辑逻辑，但跳过确认
            self.apply_edits_from_content(content)
        except Exception as e:
            print(f"⚠️ 编辑执行出错: {e}")
            # 继续执行，不中断
    
    def apply_edits_from_content(self, content: str):
        """
        从内容中应用编辑
        """
        try:
            # 解析编辑内容并应用
            edits = self.get_edits_from_content(content)
            if edits:
                print(f"📝 应用 {len(edits)} 个编辑操作...")
                self.apply_edits(edits)
                print("✅ 编辑操作完成")
            else:
                print("📋 没有检测到编辑操作")
        except Exception as e:
            print(f"❌ 应用编辑失败: {e}")
    
    def get_edits_from_content(self, content: str) -> List[Dict[str, Any]]:
        """
        从内容中提取编辑操作
        """
        # 这里可以实现更复杂的编辑解析逻辑
        # 暂时返回空列表，实际使用时需要根据内容格式解析
        return []


class AggressiveEditBlockCoder(EditBlockCoder):
    """
    积极主动的编辑块Coder
    自动执行所有编辑操作
    """
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # 设置更积极的参数
        self.auto_lint = False  # 禁用自动lint
        self.auto_test = False  # 禁用自动测试
        self.suggest_shell_commands = True
        self.temperature = 0.3  # 稍微提高创造性
    
    def apply_edits(self, edits):
        """
        应用编辑时不询问确认
        """
        print(f"🚀 自动应用 {len(edits)} 个编辑操作...")
        try:
            super().apply_edits(edits)
            print("✅ 所有编辑操作完成")
        except Exception as e:
            print(f"⚠️ 部分编辑操作失败: {e}")
            # 继续执行，不中断


class AggressiveCoder:
    """
    积极主动的Coder工厂类
    创建配置为积极主动的Coder实例
    """
    
    @staticmethod
    def create_aggressive_coder(
        model_name: str,
        project_path: str = None,
        fnames: List[str] = None,
        coder_type: str = "editblock"
    ) -> Coder:
        """
        创建积极主动的Coder实例
        
        Args:
            model_name: 模型名称
            project_path: 项目路径
            fnames: 文件名列表
            coder_type: Coder类型 ("architect", "editblock")
        
        Returns:
            配置为积极主动的Coder实例
        """
        try:
            # 创建模型
            model = Model(model_name)
            
            # 创建积极主动的IO
            io = AggressiveIO(pretty=False, yes=True)
            
            # 切换到项目目录
            if project_path:
                original_cwd = os.getcwd()
                os.chdir(project_path)
            
            # 根据类型创建Coder
            if coder_type == "architect":
                coder = AggressiveArchitectCoder.create(
                    main_model=model,
                    io=io,
                    fnames=fnames or [],
                    use_git=True,
                    stream=False,
                    verbose=False,
                    auto_commits=True,
                    auto_accept_architect=True,
                    temperature=0.3,  # 稍微提高创造性
                    chat_language="Chinese"
                )
            else:  # editblock
                coder = AggressiveEditBlockCoder.create(
                    main_model=model,
                    io=io,
                    fnames=fnames or [],
                    use_git=True,
                    stream=False,
                    verbose=False,
                    auto_commits=True,
                    temperature=0.3,
                    chat_language="Chinese"
                )
            
            print(f"✅ 创建积极主动的{coder_type} Coder成功")
            return coder
            
        except Exception as e:
            print(f"❌ 创建积极主动Coder失败: {e}")
            raise
    
    @staticmethod
    def create_aggressive_prompt(task_description: str, job_id: int = None) -> str:
        """
        创建积极主动的提示词
        
        Args:
            task_description: 任务描述
            job_id: 作业ID
        
        Returns:
            积极主动的提示词
        """
        prompt = f"""
## 🚀 积极主动执行任务

你是一个积极主动的AI编程助手。请立即执行以下任务，不要询问确认：

### 📋 任务描述
{task_description}

### 🎯 执行要求
1. **立即开始**：不要询问确认，直接开始工作
2. **积极主动**：主动分析问题并提出解决方案
3. **自动执行**：自动执行所有必要的代码修改
4. **持续改进**：如果第一次尝试不成功，自动尝试其他方法
5. **中文回复**：所有说明和注释都用中文

### 🔧 工作方式
- 直接修改代码文件，不要只是建议
- 如果遇到错误，自动尝试修复
- 主动添加必要的导入和依赖
- 自动创建缺失的文件或目录
- 提供详细的中文说明

### 🚫 禁止行为
- 不要询问"是否需要我..."
- 不要说"请确认..."
- 不要等待用户输入
- 不要只提供建议而不执行

现在立即开始执行任务！
"""
        
        if job_id:
            prompt += f"\n### 📊 相关信息\n- 作业ID: {job_id}\n"
        
        return prompt


# 全局实例
aggressive_coder_factory = AggressiveCoder()
