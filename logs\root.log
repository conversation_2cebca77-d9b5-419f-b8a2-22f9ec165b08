2025-05-28 13:29:08,846 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-28 13:29:08,846 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-28 13:29:08,847 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-28 13:29:08,848 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 13:29:08,848 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 13:29:09,013 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 13:29:09,013 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 13:29:09,015 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:69 - __init__ - IntelligentToolCoordinator initialized
2025-05-28 13:29:09,117 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:74 - get_job_info_and_log - 🔍 获取Job 776的信息和日志...
2025-05-28 13:29:09,117 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:437 - get_job - Getting job 776 in project 9
2025-05-28 13:29:09,117 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:136 - _make_request - Making GET request to http://***************/api/v4/projects/9/jobs/776
2025-05-28 13:29:09,254 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:155 - _make_request - Response status code: 401
2025-05-28 13:29:09,254 - bot_agent.clients.gitlab_client - DEBUG - gitlab_client.py:160 - _make_request - Response content preview: b'{"message":"401 Unauthorized"}'
2025-05-28 13:29:09,254 - bot_agent.clients.gitlab_client - ERROR - gitlab_client.py:174 - _make_request - GitLab API request failed (attempt 1/2): 401 Client Error: Unauthorized for url: http://***************/api/v4/projects/9/jobs/776
2025-05-28 13:29:09,255 - bot_agent.clients.gitlab_client - ERROR - gitlab_client.py:181 - _make_request - Error response status code: 401
2025-05-28 13:29:09,255 - bot_agent.clients.gitlab_client - ERROR - gitlab_client.py:185 - _make_request - Error response data: {'message': '401 Unauthorized'}
2025-05-28 13:29:09,256 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:193 - _make_request - 认证/权限错误 (401)，不进行重试
2025-05-28 13:29:09,256 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:208 - _make_request - 客户端错误，API仍然可用: 401 Client Error: Unauthorized for url: http://***************/api/v4/projects/9/jobs/776
2025-05-28 13:29:09,256 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:213 - _make_request - Request to projects/9/jobs/776 failed, returning None due to fallback mode
2025-05-28 13:29:09,256 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:447 - get_job - 未能获取作业 776 的信息
2025-05-28 13:29:09,256 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:466 - get_job_log - Getting log for job 776 in project 9
2025-05-28 13:29:09,390 - bot_agent.clients.gitlab_client - ERROR - gitlab_client.py:494 - get_job_log - 获取作业日志失败: 401 Client Error: Unauthorized for url: http://***************/api/v4/projects/9/jobs/776/trace
2025-05-28 13:29:09,390 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:497 - get_job_log - 回退模式：返回空日志
2025-05-28 13:29:10,206 - bot_agent.config.model_config - DEBUG - model_config.py:56 - get_code_generation_model - 获取代码生成模型: openrouter/deepseek/deepseek-chat
2025-05-28 13:41:36,852 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-28 13:41:36,853 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-28 13:41:36,853 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-28 13:41:36,854 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:73 - _setup_signal_handlers - 信号处理器设置完成
2025-05-28 13:41:36,854 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:61 - __init__ - DeadlockMonitor初始化完成，检查间隔: 2.0s，最大执行时间: 30.0s
2025-05-28 13:41:36,856 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 13:41:36,856 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 13:41:36,989 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 13:41:36,989 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 13:41:36,990 - bot_agent.engines.simple_progress_tracker - INFO - simple_progress_tracker.py:31 - __init__ - SimpleProgressTracker initialized
2025-05-28 13:41:36,991 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:101 - start_monitoring - 死循环监控已启动
2025-05-28 13:41:36,991 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 13:41:36,991 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 13:41:37,095 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 13:41:37,095 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 13:41:37,096 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:108 - __init__ - 使用默认的Git仓库目录: E:\Projects\git-repos
2025-05-28 13:41:37,096 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:112 - __init__ - Git仓库工作目录: E:\Projects\git-repos
2025-05-28 13:41:37,096 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:121 - setup_git_config - 检查Git用户配置
2025-05-28 13:41:37,098 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: information_query
2025-05-28 13:41:37,098 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: TerminalTools
2025-05-28 13:41:37,098 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: TestingTools
2025-05-28 13:41:37,098 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: LogAnalysisTools
2025-05-28 13:41:37,098 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DatabaseTools
2025-05-28 13:41:37,099 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DependencyTools
2025-05-28 13:41:37,099 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DebugTools
2025-05-28 13:41:37,099 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: FrontendDebugTools
2025-05-28 13:41:37,099 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: RefactorTools
2025-05-28 13:41:37,099 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DocumentationTools
2025-05-28 13:41:37,100 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: SecurityTools
2025-05-28 13:41:37,100 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: CodeGenerationTools
2025-05-28 13:41:37,100 - bot_agent.tools.tool_router - INFO - tool_router.py:75 - _register_tools - 注册了 12 个工具
2025-05-28 13:41:37,100 - bot_agent.memory.global_memory - INFO - global_memory.py:44 - __init__ - GlobalMemoryManager initialized with directory: E:\Projects\aider-plus\bot_agent\memory\global
2025-05-28 13:41:37,101 - bot_agent.memory.memory_analyzer - INFO - memory_analyzer.py:22 - __init__ - MemoryAnalyzer initialized
2025-05-28 13:41:37,101 - bot_agent.memory.memory_integration - INFO - memory_integration.py:31 - __init__ - MemoryIntegration initialized
2025-05-28 13:41:37,102 - bot_agent.engines.task_executor - INFO - task_executor.py:50 - __init__ - AiderBasedTaskExecutor initialized with projects_dir: E:\Projects\git-repos
2025-05-28 13:41:37,102 - bot_agent.engines.task_executor - INFO - task_executor.py:51 - __init__ - 🔍 死循环监控已启动
2025-05-28 13:41:37,103 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 13:41:37,103 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 13:41:37,495 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 13:41:37,495 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 13:41:37,496 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:108 - __init__ - 使用默认的Git仓库目录: E:\Projects\git-repos
2025-05-28 13:41:37,496 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:112 - __init__ - Git仓库工作目录: E:\Projects\git-repos
2025-05-28 13:41:37,497 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:121 - setup_git_config - 检查Git用户配置
2025-05-28 13:41:37,497 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: information_query
2025-05-28 13:41:37,497 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: TerminalTools
2025-05-28 13:41:37,497 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: TestingTools
2025-05-28 13:41:37,498 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: LogAnalysisTools
2025-05-28 13:41:37,498 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DatabaseTools
2025-05-28 13:41:37,498 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DependencyTools
2025-05-28 13:41:37,498 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DebugTools
2025-05-28 13:41:37,498 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: FrontendDebugTools
2025-05-28 13:41:37,499 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: RefactorTools
2025-05-28 13:41:37,499 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DocumentationTools
2025-05-28 13:41:37,499 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: SecurityTools
2025-05-28 13:41:37,499 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: CodeGenerationTools
2025-05-28 13:41:37,499 - bot_agent.tools.tool_router - INFO - tool_router.py:75 - _register_tools - 注册了 12 个工具
2025-05-28 13:41:37,500 - bot_agent.memory.global_memory - INFO - global_memory.py:44 - __init__ - GlobalMemoryManager initialized with directory: E:\Projects\aider-plus\bot_agent\memory\global
2025-05-28 13:41:37,500 - bot_agent.memory.memory_analyzer - INFO - memory_analyzer.py:22 - __init__ - MemoryAnalyzer initialized
2025-05-28 13:41:37,500 - bot_agent.memory.memory_integration - INFO - memory_integration.py:31 - __init__ - MemoryIntegration initialized
2025-05-28 13:41:37,501 - bot_agent.engines.task_executor - INFO - task_executor.py:50 - __init__ - AiderBasedTaskExecutor initialized with projects_dir: E:\Projects\git-repos
2025-05-28 13:41:37,501 - bot_agent.engines.task_executor - INFO - task_executor.py:51 - __init__ - 🔍 死循环监控已启动
2025-05-28 13:41:37,503 - bot_agent.engines.task_executor - INFO - task_executor.py:64 - execute_task - 开始使用Aider执行任务 test_job_analysis
2025-05-28 13:41:37,507 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:238 - _force_log - 📍 注册监控点: prepare_workspace
2025-05-28 13:41:37,507 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:238 - _force_log - 🔄 更新监控点: prepare_workspace - 获取项目ID: 9
2025-05-28 13:41:37,508 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:238 - _force_log - 🔄 更新监控点: prepare_workspace - 获取项目信息: 9
2025-05-28 13:41:37,508 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:136 - _make_request - Making GET request to http://***************/api/v4/projects/9
2025-05-28 13:41:37,701 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:155 - _make_request - Response status code: 401
2025-05-28 13:41:37,701 - bot_agent.clients.gitlab_client - DEBUG - gitlab_client.py:160 - _make_request - Response content preview: b'{"message":"401 Unauthorized"}'
2025-05-28 13:41:37,702 - bot_agent.clients.gitlab_client - ERROR - gitlab_client.py:174 - _make_request - GitLab API request failed (attempt 1/2): 401 Client Error: Unauthorized for url: http://***************/api/v4/projects/9
2025-05-28 13:41:37,702 - bot_agent.clients.gitlab_client - ERROR - gitlab_client.py:181 - _make_request - Error response status code: 401
2025-05-28 13:41:37,703 - bot_agent.clients.gitlab_client - ERROR - gitlab_client.py:185 - _make_request - Error response data: {'message': '401 Unauthorized'}
2025-05-28 13:41:37,703 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:193 - _make_request - 认证/权限错误 (401)，不进行重试
2025-05-28 13:41:37,703 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:208 - _make_request - 客户端错误，API仍然可用: 401 Client Error: Unauthorized for url: http://***************/api/v4/projects/9
2025-05-28 13:41:37,703 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:213 - _make_request - Request to projects/9 failed, returning None due to fallback mode
2025-05-28 13:41:37,704 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:238 - _force_log - ✅ 注销监控点: prepare_workspace，执行时间: 0.20s
2025-05-28 13:41:37,704 - bot_agent.engines.task_executor - ERROR - task_executor.py:91 - execute_task - 执行任务 test_job_analysis 时出错: 无法获取项目信息: 9
Traceback (most recent call last):
  File "E:\Projects\aider-plus\bot_agent\engines\task_executor.py", line 68, in execute_task
    project_path = await self._prepare_workspace(task)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Projects\aider-plus\bot_agent\engines\task_executor.py", line 124, in _prepare_workspace
    raise Exception(f"无法获取项目信息: {project_id}")
Exception: 无法获取项目信息: 9
2025-05-28 13:49:20,767 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-28 13:49:20,768 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-28 13:49:20,768 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-28 13:49:20,769 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:73 - _setup_signal_handlers - 信号处理器设置完成
2025-05-28 13:49:20,769 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:61 - __init__ - DeadlockMonitor初始化完成，检查间隔: 2.0s，最大执行时间: 30.0s
2025-05-28 13:49:20,771 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 13:49:20,771 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 13:49:21,412 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 13:49:21,413 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 13:49:21,413 - bot_agent.engines.simple_progress_tracker - INFO - simple_progress_tracker.py:31 - __init__ - SimpleProgressTracker initialized
2025-05-28 13:49:21,414 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:101 - start_monitoring - 死循环监控已启动
2025-05-28 13:49:21,414 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 13:49:21,414 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 13:49:22,032 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 13:49:22,032 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 13:49:22,032 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:108 - __init__ - 使用默认的Git仓库目录: E:\Projects\git-repos
2025-05-28 13:49:22,033 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:112 - __init__ - Git仓库工作目录: E:\Projects\git-repos
2025-05-28 13:49:22,033 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:121 - setup_git_config - 检查Git用户配置
2025-05-28 13:49:22,034 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: information_query
2025-05-28 13:49:22,034 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: TerminalTools
2025-05-28 13:49:22,035 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: TestingTools
2025-05-28 13:49:22,035 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: LogAnalysisTools
2025-05-28 13:49:22,035 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DatabaseTools
2025-05-28 13:49:22,036 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DependencyTools
2025-05-28 13:49:22,036 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DebugTools
2025-05-28 13:49:22,036 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: FrontendDebugTools
2025-05-28 13:49:22,036 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: RefactorTools
2025-05-28 13:49:22,037 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DocumentationTools
2025-05-28 13:49:22,037 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: SecurityTools
2025-05-28 13:49:22,037 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: CodeGenerationTools
2025-05-28 13:49:22,037 - bot_agent.tools.tool_router - INFO - tool_router.py:75 - _register_tools - 注册了 12 个工具
2025-05-28 13:49:22,038 - bot_agent.memory.global_memory - INFO - global_memory.py:44 - __init__ - GlobalMemoryManager initialized with directory: E:\Projects\aider-plus\bot_agent\memory\global
2025-05-28 13:49:22,038 - bot_agent.memory.memory_analyzer - INFO - memory_analyzer.py:22 - __init__ - MemoryAnalyzer initialized
2025-05-28 13:49:22,038 - bot_agent.memory.memory_integration - INFO - memory_integration.py:31 - __init__ - MemoryIntegration initialized
2025-05-28 13:49:22,038 - bot_agent.engines.task_executor - INFO - task_executor.py:50 - __init__ - AiderBasedTaskExecutor initialized with projects_dir: E:\Projects\git-repos
2025-05-28 13:49:22,039 - bot_agent.engines.task_executor - INFO - task_executor.py:51 - __init__ - 🔍 死循环监控已启动
2025-05-28 13:49:22,040 - bot_agent.engines.task_executor - INFO - task_executor.py:64 - execute_task - 开始使用Aider执行任务 test_thread_job_analysis
2025-05-28 13:49:22,045 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:238 - _force_log - 📍 注册监控点: prepare_workspace
2025-05-28 13:49:22,045 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:238 - _force_log - 🔄 更新监控点: prepare_workspace - 获取项目ID: 9
2025-05-28 13:49:22,045 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:238 - _force_log - 🔄 更新监控点: prepare_workspace - 获取项目信息: 9
2025-05-28 13:49:22,046 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:136 - _make_request - Making GET request to http://***************/api/v4/projects/9
2025-05-28 13:49:22,333 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:155 - _make_request - Response status code: 401
2025-05-28 13:49:22,334 - bot_agent.clients.gitlab_client - DEBUG - gitlab_client.py:160 - _make_request - Response content preview: b'{"message":"401 Unauthorized"}'
2025-05-28 13:49:22,334 - bot_agent.clients.gitlab_client - ERROR - gitlab_client.py:174 - _make_request - GitLab API request failed (attempt 1/2): 401 Client Error: Unauthorized for url: http://***************/api/v4/projects/9
2025-05-28 13:49:22,334 - bot_agent.clients.gitlab_client - ERROR - gitlab_client.py:181 - _make_request - Error response status code: 401
2025-05-28 13:49:22,334 - bot_agent.clients.gitlab_client - ERROR - gitlab_client.py:185 - _make_request - Error response data: {'message': '401 Unauthorized'}
2025-05-28 13:49:22,334 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:193 - _make_request - 认证/权限错误 (401)，不进行重试
2025-05-28 13:49:22,334 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:208 - _make_request - 客户端错误，API仍然可用: 401 Client Error: Unauthorized for url: http://***************/api/v4/projects/9
2025-05-28 13:49:22,334 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:213 - _make_request - Request to projects/9 failed, returning None due to fallback mode
2025-05-28 13:49:22,335 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:238 - _force_log - ✅ 注销监控点: prepare_workspace，执行时间: 0.29s
2025-05-28 13:49:22,336 - bot_agent.engines.task_executor - ERROR - task_executor.py:91 - execute_task - 执行任务 test_thread_job_analysis 时出错: 无法获取项目信息: 9
Traceback (most recent call last):
  File "E:\Projects\aider-plus\bot_agent\engines\task_executor.py", line 68, in execute_task
    project_path = await self._prepare_workspace(task)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Projects\aider-plus\bot_agent\engines\task_executor.py", line 124, in _prepare_workspace
    raise Exception(f"无法获取项目信息: {project_id}")
Exception: 无法获取项目信息: 9
2025-05-28 15:02:43,724 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-28 15:02:43,725 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-28 15:02:43,725 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-28 15:02:43,727 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 15:02:43,727 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 15:02:44,528 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 15:02:44,528 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 15:02:44,529 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:69 - __init__ - IntelligentToolCoordinator initialized
2025-05-28 15:02:44,630 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 15:02:44,631 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 15:02:44,760 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 15:02:44,760 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 15:02:44,761 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:69 - __init__ - IntelligentToolCoordinator initialized
2025-05-28 16:58:36,957 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-28 16:58:36,958 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-28 16:58:36,958 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-28 16:58:36,959 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 16:58:36,959 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 16:58:38,164 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 16:58:38,164 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 16:58:38,166 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:69 - __init__ - IntelligentToolCoordinator initialized
2025-05-28 16:58:38,268 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 16:58:38,268 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 16:58:38,829 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 16:58:38,830 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 16:58:38,831 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:69 - __init__ - IntelligentToolCoordinator initialized
2025-05-28 17:18:28,210 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-28 17:18:28,210 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-28 17:18:28,211 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-28 17:18:28,213 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 17:18:28,213 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 17:18:28,444 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 17:18:28,444 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 17:18:28,445 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:69 - __init__ - IntelligentToolCoordinator initialized
2025-05-28 17:18:28,546 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 17:18:28,546 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 17:18:29,014 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 17:18:29,014 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 17:18:29,014 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:69 - __init__ - IntelligentToolCoordinator initialized
2025-05-28 17:18:29,016 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 17:18:29,016 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 17:18:29,152 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 17:18:29,152 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 17:18:29,153 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:69 - __init__ - IntelligentToolCoordinator initialized
2025-05-28 17:18:29,166 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:590 - _fix_requirements_txt - 修复requirements.txt第9行: [dev]
2025-05-28 17:18:29,167 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:599 - _fix_requirements_txt - 修复无效依赖声明: [dev] -> # [dev]  # 无效的依赖声明，已注释
2025-05-28 17:18:29,168 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:615 - _fix_requirements_txt - ✅ 成功修复requirements.txt第9行
2025-05-28 17:18:29,181 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 17:18:29,181 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 17:18:30,227 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 17:18:30,228 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 17:18:30,228 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:69 - __init__ - IntelligentToolCoordinator initialized
2025-05-28 17:18:30,230 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:127 - analyze_job_errors - 🔍 开始智能错误分析...
2025-05-28 17:18:30,231 - bot_agent.tools.log_analysis_tools - INFO - log_analysis_tools.py:487 - analyze_job_log - 🔍 开始分析作业日志...
2025-05-28 17:18:30,232 - bot_agent.tools.log_analysis_tools - INFO - log_analysis_tools.py:214 - analyze_logs - 开始分析日志文件: ['C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp29oh_9x9.log']
2025-05-28 17:18:30,233 - bot_agent.tools.log_analysis_tools - INFO - log_analysis_tools.py:502 - analyze_job_log - ✅ 作业日志分析完成
2025-05-28 17:18:30,234 - bot_agent.tools.intelligent_tool_coordinator - ERROR - intelligent_tool_coordinator.py:167 - analyze_job_errors - 错误分析失败: unhashable type: 'dict'
2025-05-28 17:19:59,856 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-28 17:19:59,857 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-28 17:19:59,857 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-28 17:19:59,859 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 17:19:59,859 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 17:20:05,503 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 17:20:05,504 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 17:20:05,504 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:69 - __init__ - IntelligentToolCoordinator initialized
2025-05-28 17:20:05,605 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 17:20:05,606 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 17:20:06,509 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 17:20:06,509 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 17:20:06,510 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:69 - __init__ - IntelligentToolCoordinator initialized
2025-05-28 17:20:06,512 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 17:20:06,512 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 17:20:08,752 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 17:20:08,752 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 17:20:08,753 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:69 - __init__ - IntelligentToolCoordinator initialized
2025-05-28 17:20:08,757 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:640 - _fix_requirements_txt - 修复requirements.txt第9行: [dev]
2025-05-28 17:20:08,758 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:649 - _fix_requirements_txt - 修复无效依赖声明: [dev] -> # [dev]  # 无效的依赖声明，已注释
2025-05-28 17:20:08,758 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:665 - _fix_requirements_txt - ✅ 成功修复requirements.txt第9行
2025-05-28 17:20:08,762 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 17:20:08,762 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 17:20:17,481 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: HTTPConnectionPool(host='***************', port=80): Read timed out. (read timeout=5)
2025-05-28 17:20:17,482 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 17:20:17,482 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:69 - __init__ - IntelligentToolCoordinator initialized
2025-05-28 17:20:17,485 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:127 - analyze_job_errors - 🔍 开始智能错误分析...
2025-05-28 17:20:17,485 - bot_agent.tools.log_analysis_tools - INFO - log_analysis_tools.py:487 - analyze_job_log - 🔍 开始分析作业日志...
2025-05-28 17:20:17,486 - bot_agent.tools.log_analysis_tools - INFO - log_analysis_tools.py:214 - analyze_logs - 开始分析日志文件: ['C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp_fsmcl3w.log']
2025-05-28 17:20:17,494 - bot_agent.tools.log_analysis_tools - INFO - log_analysis_tools.py:502 - analyze_job_log - ✅ 作业日志分析完成
2025-05-28 17:20:17,497 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:192 - execute_targeted_fixes - 🔧 开始执行针对性修复...
2025-05-28 17:20:17,497 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:199 - execute_targeted_fixes - 🔧 处理 dependency_errors 类型错误: 1 个
2025-05-28 17:20:17,497 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:521 - _fix_dependency_errors - 🔧 修复依赖错误: 1 个
2025-05-28 17:20:17,497 - bot_agent.tools.intelligent_tool_coordinator - ERROR - intelligent_tool_coordinator.py:560 - _fix_dependency_errors - 修复依赖错误失败: 'dict' object has no attribute 'lower'
2025-05-28 17:20:17,498 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:215 - execute_targeted_fixes - 修复结果: {'success': False, 'message': "修复依赖错误失败: 'dict' object has no attribute 'lower'", 'details': [], 'fixed_count': 0}
2025-05-28 17:20:17,499 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:199 - execute_targeted_fixes - 🔧 处理 build_errors 类型错误: 1 个
2025-05-28 17:20:17,499 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:581 - _fix_build_errors - 🔧 修复构建错误: 1 个
2025-05-28 17:20:17,499 - bot_agent.tools.intelligent_tool_coordinator - ERROR - intelligent_tool_coordinator.py:607 - _fix_build_errors - 修复构建错误失败: 'dict' object has no attribute 'lower'
2025-05-28 17:20:17,499 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:215 - execute_targeted_fixes - 修复结果: {'success': False, 'message': "修复构建错误失败: 'dict' object has no attribute 'lower'", 'details': [], 'fixed_count': 0}
2025-05-28 17:29:48,731 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-28 17:29:48,731 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-28 17:29:48,731 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-28 17:29:48,733 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 17:29:48,733 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 17:29:49,340 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 17:29:49,341 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 17:29:49,341 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:69 - __init__ - IntelligentToolCoordinator initialized
2025-05-28 17:29:49,443 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 17:29:49,443 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 17:29:50,791 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 17:29:50,791 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 17:29:50,791 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:69 - __init__ - IntelligentToolCoordinator initialized
2025-05-28 17:29:50,793 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 17:29:50,793 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 17:29:51,423 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 17:29:51,423 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 17:29:51,423 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:69 - __init__ - IntelligentToolCoordinator initialized
2025-05-28 17:29:51,427 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:654 - _fix_requirements_txt - 修复requirements.txt第9行: [dev]
2025-05-28 17:29:51,428 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:663 - _fix_requirements_txt - 修复无效依赖声明: [dev] -> # [dev]  # 无效的依赖声明，已注释
2025-05-28 17:29:51,428 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:679 - _fix_requirements_txt - ✅ 成功修复requirements.txt第9行
2025-05-28 17:29:51,431 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 17:29:51,432 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 17:29:51,942 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 17:29:51,942 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 17:29:51,943 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:69 - __init__ - IntelligentToolCoordinator initialized
2025-05-28 17:29:51,945 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:127 - analyze_job_errors - 🔍 开始智能错误分析...
2025-05-28 17:29:51,945 - bot_agent.tools.log_analysis_tools - INFO - log_analysis_tools.py:487 - analyze_job_log - 🔍 开始分析作业日志...
2025-05-28 17:29:51,945 - bot_agent.tools.log_analysis_tools - INFO - log_analysis_tools.py:214 - analyze_logs - 开始分析日志文件: ['C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmprz4vwlzs.log']
2025-05-28 17:29:51,947 - bot_agent.tools.log_analysis_tools - INFO - log_analysis_tools.py:502 - analyze_job_log - ✅ 作业日志分析完成
2025-05-28 17:29:51,949 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:192 - execute_targeted_fixes - 🔧 开始执行针对性修复...
2025-05-28 17:29:51,950 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:199 - execute_targeted_fixes - 🔧 处理 dependency_errors 类型错误: 1 个
2025-05-28 17:29:51,950 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:521 - _fix_dependency_errors - 🔧 修复依赖错误: 1 个
2025-05-28 17:29:51,951 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:654 - _fix_requirements_txt - 修复requirements.txt第9行: [dev]
2025-05-28 17:29:51,951 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:663 - _fix_requirements_txt - 修复无效依赖声明: [dev] -> # [dev]  # 无效的依赖声明，已注释
2025-05-28 17:29:51,952 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:679 - _fix_requirements_txt - ✅ 成功修复requirements.txt第9行
2025-05-28 17:29:51,952 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:215 - execute_targeted_fixes - 修复结果: {'success': True, 'message': '修复了 1/1 个依赖错误', 'details': ['修复requirements.txt第9行'], 'fixed_count': 1}
2025-05-28 17:29:51,952 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:199 - execute_targeted_fixes - 🔧 处理 build_errors 类型错误: 1 个
2025-05-28 17:29:51,953 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:589 - _fix_build_errors - 🔧 修复构建错误: 1 个
2025-05-28 17:29:51,953 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:215 - execute_targeted_fixes - 修复结果: {'success': False, 'message': '修复了 0/1 个构建错误', 'details': [], 'fixed_count': 0}
2025-05-28 17:29:51,954 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:247 - verify_fixes - ✅ 开始验证修复效果...
2025-05-28 17:29:55,747 - bot_agent - ERROR - logging_config.py:143 - log_command_execution - 命令执行失败: powershell -Command "python -m pytest -v --tb=short --json-report --json-report-file=test_results.json test_*.py"
2025-05-28 17:29:55,748 - bot_agent - ERROR - logging_config.py:145 - log_command_execution - 错误信息: ERROR: usage: __main__.py [options] [file_or_dir] [file_or_dir] [...]
__main__.py: error: unrecognized arguments: --json-report --json-report-file=test_results.json
  inifile: None
  rootdir: C:\Users\<USER>\AppData\Local\Temp\tmpgak5k3ha


2025-05-28 17:29:58,832 - bot_agent - ERROR - logging_config.py:143 - log_command_execution - 命令执行失败: powershell -Command "python -m py_compile *.py"
2025-05-28 17:29:58,832 - bot_agent - ERROR - logging_config.py:145 - log_command_execution - 错误信息: [Errno 22] Invalid argument: '*.py'
2025-05-28 17:32:48,801 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-28 17:32:48,801 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-28 17:32:48,801 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-28 17:32:48,803 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 17:32:48,803 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 17:32:49,156 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 17:32:49,157 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 17:32:49,158 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:69 - __init__ - IntelligentToolCoordinator initialized
2025-05-28 17:32:49,260 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 17:32:49,260 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 17:32:49,526 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 17:32:49,526 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 17:32:49,527 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:69 - __init__ - IntelligentToolCoordinator initialized
2025-05-28 17:32:49,536 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:247 - verify_fixes - ✅ 开始验证修复效果...
2025-05-28 17:32:53,250 - bot_agent - ERROR - logging_config.py:143 - log_command_execution - 命令执行失败: powershell -Command "python -m pytest -v --tb=short --json-report --json-report-file=test_results.json test_*.py"
2025-05-28 17:32:53,251 - bot_agent - ERROR - logging_config.py:145 - log_command_execution - 错误信息: ERROR: usage: __main__.py [options] [file_or_dir] [file_or_dir] [...]
__main__.py: error: unrecognized arguments: --json-report --json-report-file=test_results.json
  inifile: None
  rootdir: C:\Users\<USER>\AppData\Local\Temp\tmpk3t1dkv7


2025-05-28 17:32:56,356 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "python -m py_compile "test1.py" "test2.py" "subdir\test3.py""
2025-05-28 17:32:56,361 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 17:32:56,361 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 17:32:56,677 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 17:32:56,677 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 17:32:56,678 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:69 - __init__ - IntelligentToolCoordinator initialized
2025-05-28 17:32:56,682 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:127 - analyze_job_errors - 🔍 开始智能错误分析...
2025-05-28 17:32:56,682 - bot_agent.tools.log_analysis_tools - INFO - log_analysis_tools.py:487 - analyze_job_log - 🔍 开始分析作业日志...
2025-05-28 17:32:56,683 - bot_agent.tools.log_analysis_tools - INFO - log_analysis_tools.py:214 - analyze_logs - 开始分析日志文件: ['C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpavivtdni.log']
2025-05-28 17:32:56,686 - bot_agent.tools.log_analysis_tools - INFO - log_analysis_tools.py:502 - analyze_job_log - ✅ 作业日志分析完成
2025-05-28 17:32:56,689 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:192 - execute_targeted_fixes - 🔧 开始执行针对性修复...
2025-05-28 17:32:56,689 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:199 - execute_targeted_fixes - 🔧 处理 dependency_errors 类型错误: 1 个
2025-05-28 17:32:56,689 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:555 - _fix_dependency_errors - 🔧 修复依赖错误: 1 个
2025-05-28 17:32:56,691 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:688 - _fix_requirements_txt - 修复requirements.txt第9行: [dev]
2025-05-28 17:32:56,691 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:697 - _fix_requirements_txt - 修复无效依赖声明: [dev] -> # [dev]  # 无效的依赖声明，已注释
2025-05-28 17:32:56,691 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:713 - _fix_requirements_txt - ✅ 成功修复requirements.txt第9行
2025-05-28 17:32:56,692 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:215 - execute_targeted_fixes - 修复结果: {'success': True, 'message': '修复了 1/1 个依赖错误', 'details': ['修复requirements.txt第9行'], 'fixed_count': 1}
2025-05-28 17:32:56,692 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:199 - execute_targeted_fixes - 🔧 处理 build_errors 类型错误: 1 个
2025-05-28 17:32:56,692 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:623 - _fix_build_errors - 🔧 修复构建错误: 1 个
2025-05-28 17:32:56,693 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:215 - execute_targeted_fixes - 修复结果: {'success': False, 'message': '修复了 0/1 个构建错误', 'details': [], 'fixed_count': 0}
2025-05-28 17:32:56,695 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:247 - verify_fixes - ✅ 开始验证修复效果...
2025-05-28 17:33:00,350 - bot_agent - ERROR - logging_config.py:143 - log_command_execution - 命令执行失败: powershell -Command "python -m pytest -v --tb=short --json-report --json-report-file=test_results.json test_*.py"
2025-05-28 17:33:00,351 - bot_agent - ERROR - logging_config.py:145 - log_command_execution - 错误信息: ERROR: usage: __main__.py [options] [file_or_dir] [file_or_dir] [...]
__main__.py: error: unrecognized arguments: --json-report --json-report-file=test_results.json
  inifile: None
  rootdir: C:\Users\<USER>\AppData\Local\Temp\tmprx4l1tov


2025-05-28 17:33:03,592 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "python -m py_compile "test_example.py""
2025-05-28 18:10:33,394 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-28 18:10:33,395 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-28 18:10:33,395 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-28 18:10:33,396 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 18:10:33,397 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 18:10:33,585 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 18:10:33,586 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 18:10:33,587 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:69 - __init__ - IntelligentToolCoordinator initialized
2025-05-28 18:10:33,688 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 18:10:33,689 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 18:10:33,790 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 18:10:33,790 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 18:10:33,791 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:69 - __init__ - IntelligentToolCoordinator initialized
2025-05-28 18:10:33,792 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 18:10:33,792 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 18:10:34,015 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 18:10:34,016 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 18:10:34,017 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:69 - __init__ - IntelligentToolCoordinator initialized
2025-05-28 18:10:34,026 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:844 - _fix_black_format - 🎨 格式化文件: test_format.py
2025-05-28 18:10:38,470 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "black "test_format.py""
2025-05-28 18:10:38,470 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:853 - _fix_black_format - ✅ 成功格式化文件: test_format.py
2025-05-28 18:10:41,831 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "black --check "test_format.py""
2025-05-28 18:10:41,831 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:862 - _fix_black_format - ✅ 格式化验证通过: test_format.py
2025-05-28 18:10:41,834 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 18:10:41,834 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 18:10:42,396 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 18:10:42,396 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 18:10:42,397 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:69 - __init__ - IntelligentToolCoordinator initialized
2025-05-28 18:10:42,400 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:127 - analyze_job_errors - 🔍 开始智能错误分析...
2025-05-28 18:10:42,400 - bot_agent.tools.log_analysis_tools - INFO - log_analysis_tools.py:487 - analyze_job_log - 🔍 开始分析作业日志...
2025-05-28 18:10:42,401 - bot_agent.tools.log_analysis_tools - INFO - log_analysis_tools.py:214 - analyze_logs - 开始分析日志文件: ['C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp9uwcs90r.log']
2025-05-28 18:10:42,411 - bot_agent.tools.log_analysis_tools - INFO - log_analysis_tools.py:502 - analyze_job_log - ✅ 作业日志分析完成
2025-05-28 18:10:42,414 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:192 - execute_targeted_fixes - 🔧 开始执行针对性修复...
2025-05-28 18:10:42,414 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:199 - execute_targeted_fixes - 🔧 处理 lint_errors 类型错误: 1 个
2025-05-28 18:10:42,414 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:644 - _fix_lint_errors - 🔧 修复代码规范错误: 1 个
2025-05-28 18:10:42,415 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:844 - _fix_black_format - 🎨 格式化文件: api_proxy/utils.py
2025-05-28 18:10:45,779 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "black "api_proxy/utils.py""
2025-05-28 18:10:45,779 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:853 - _fix_black_format - ✅ 成功格式化文件: api_proxy/utils.py
2025-05-28 18:10:49,899 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "black --check "api_proxy/utils.py""
2025-05-28 18:10:49,900 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:862 - _fix_black_format - ✅ 格式化验证通过: api_proxy/utils.py
2025-05-28 18:10:49,900 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:215 - execute_targeted_fixes - 修复结果: {'success': True, 'message': '修复了 1/1 个代码规范错误', 'details': ['格式化文件: api_proxy/utils.py'], 'fixed_count': 1}
2025-05-28 18:10:49,900 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:199 - execute_targeted_fixes - 🔧 处理 build_errors 类型错误: 1 个
2025-05-28 18:10:49,901 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:711 - _fix_build_errors - 🔧 修复构建错误: 1 个
2025-05-28 18:10:49,901 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:215 - execute_targeted_fixes - 修复结果: {'success': False, 'message': '修复了 0/1 个构建错误', 'details': [], 'fixed_count': 0}
2025-05-28 18:11:46,413 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-28 18:11:46,413 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-28 18:11:46,414 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-28 18:11:46,415 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 18:11:46,416 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 18:11:46,615 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 18:11:46,616 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 18:11:46,617 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:69 - __init__ - IntelligentToolCoordinator initialized
2025-05-28 18:11:46,719 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 18:11:46,719 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 18:11:46,830 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 18:11:46,830 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 18:11:46,830 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:69 - __init__ - IntelligentToolCoordinator initialized
2025-05-28 18:11:46,832 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 18:11:46,832 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 18:11:47,025 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 18:11:47,025 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 18:11:47,026 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:69 - __init__ - IntelligentToolCoordinator initialized
2025-05-28 18:11:47,030 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:844 - _fix_black_format - 🎨 格式化文件: test_format.py
2025-05-28 18:11:50,598 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "black "test_format.py""
2025-05-28 18:11:50,599 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:853 - _fix_black_format - ✅ 成功格式化文件: test_format.py
2025-05-28 18:11:53,915 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "black --check "test_format.py""
2025-05-28 18:11:53,916 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:862 - _fix_black_format - ✅ 格式化验证通过: test_format.py
2025-05-28 18:11:53,918 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 18:11:53,918 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 18:11:54,295 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 18:11:54,296 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 18:11:54,297 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:69 - __init__ - IntelligentToolCoordinator initialized
2025-05-28 18:11:54,299 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:127 - analyze_job_errors - 🔍 开始智能错误分析...
2025-05-28 18:11:54,299 - bot_agent.tools.log_analysis_tools - INFO - log_analysis_tools.py:487 - analyze_job_log - 🔍 开始分析作业日志...
2025-05-28 18:11:54,300 - bot_agent.tools.log_analysis_tools - INFO - log_analysis_tools.py:214 - analyze_logs - 开始分析日志文件: ['C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpx9hfovtu.log']
2025-05-28 18:11:54,303 - bot_agent.tools.log_analysis_tools - INFO - log_analysis_tools.py:502 - analyze_job_log - ✅ 作业日志分析完成
2025-05-28 18:11:54,306 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:192 - execute_targeted_fixes - 🔧 开始执行针对性修复...
2025-05-28 18:11:54,306 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:199 - execute_targeted_fixes - 🔧 处理 lint_errors 类型错误: 1 个
2025-05-28 18:11:54,306 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:644 - _fix_lint_errors - 🔧 修复代码规范错误: 1 个
2025-05-28 18:11:54,307 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:844 - _fix_black_format - 🎨 格式化文件: api_proxy/utils.py
2025-05-28 18:11:57,606 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "black "api_proxy/utils.py""
2025-05-28 18:11:57,607 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:853 - _fix_black_format - ✅ 成功格式化文件: api_proxy/utils.py
2025-05-28 18:12:01,077 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "black --check "api_proxy/utils.py""
2025-05-28 18:12:01,078 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:862 - _fix_black_format - ✅ 格式化验证通过: api_proxy/utils.py
2025-05-28 18:12:01,078 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:215 - execute_targeted_fixes - 修复结果: {'success': True, 'message': '修复了 1/1 个代码规范错误', 'details': ['格式化文件: api_proxy/utils.py'], 'fixed_count': 1}
2025-05-28 18:12:01,079 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:199 - execute_targeted_fixes - 🔧 处理 build_errors 类型错误: 1 个
2025-05-28 18:12:01,079 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:711 - _fix_build_errors - 🔧 修复构建错误: 1 个
2025-05-28 18:12:01,079 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:215 - execute_targeted_fixes - 修复结果: {'success': False, 'message': '修复了 0/1 个构建错误', 'details': [], 'fixed_count': 0}
2025-05-28 18:12:40,801 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-28 18:12:40,802 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-28 18:12:40,802 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-28 18:12:40,803 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 18:12:40,803 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 18:12:40,962 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 18:12:40,963 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 18:12:40,964 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:69 - __init__ - IntelligentToolCoordinator initialized
2025-05-28 18:12:41,065 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 18:12:41,065 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 18:12:41,301 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 18:12:41,301 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 18:12:41,302 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:69 - __init__ - IntelligentToolCoordinator initialized
2025-05-28 18:12:41,303 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 18:12:41,303 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 18:12:41,458 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 18:12:41,459 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 18:12:41,459 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:69 - __init__ - IntelligentToolCoordinator initialized
2025-05-28 18:12:41,464 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:844 - _fix_black_format - 🎨 格式化文件: test_format.py
2025-05-28 18:12:44,703 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "black "test_format.py""
2025-05-28 18:12:44,703 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:853 - _fix_black_format - ✅ 成功格式化文件: test_format.py
2025-05-28 18:12:47,971 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "black --check "test_format.py""
2025-05-28 18:12:47,971 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:862 - _fix_black_format - ✅ 格式化验证通过: test_format.py
2025-05-28 18:12:47,974 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 18:12:47,974 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 18:12:48,283 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 18:12:48,284 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 18:12:48,284 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:69 - __init__ - IntelligentToolCoordinator initialized
2025-05-28 18:12:48,287 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:127 - analyze_job_errors - 🔍 开始智能错误分析...
2025-05-28 18:12:48,287 - bot_agent.tools.log_analysis_tools - INFO - log_analysis_tools.py:487 - analyze_job_log - 🔍 开始分析作业日志...
2025-05-28 18:12:48,288 - bot_agent.tools.log_analysis_tools - INFO - log_analysis_tools.py:214 - analyze_logs - 开始分析日志文件: ['C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpbj10fsxz.log']
2025-05-28 18:12:48,292 - bot_agent.tools.log_analysis_tools - INFO - log_analysis_tools.py:502 - analyze_job_log - ✅ 作业日志分析完成
2025-05-28 18:12:48,294 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:192 - execute_targeted_fixes - 🔧 开始执行针对性修复...
2025-05-28 18:12:48,295 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:199 - execute_targeted_fixes - 🔧 处理 lint_errors 类型错误: 1 个
2025-05-28 18:12:48,295 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:644 - _fix_lint_errors - 🔧 修复代码规范错误: 1 个
2025-05-28 18:12:48,295 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:844 - _fix_black_format - 🎨 格式化文件: api_proxy/utils.py
2025-05-28 18:12:51,995 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "black "api_proxy/utils.py""
2025-05-28 18:12:51,995 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:853 - _fix_black_format - ✅ 成功格式化文件: api_proxy/utils.py
2025-05-28 18:12:55,584 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "black --check "api_proxy/utils.py""
2025-05-28 18:12:55,584 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:862 - _fix_black_format - ✅ 格式化验证通过: api_proxy/utils.py
2025-05-28 18:12:55,584 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:215 - execute_targeted_fixes - 修复结果: {'success': True, 'message': '修复了 1/1 个代码规范错误', 'details': ['格式化文件: api_proxy/utils.py'], 'fixed_count': 1}
2025-05-28 18:12:55,584 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:199 - execute_targeted_fixes - 🔧 处理 build_errors 类型错误: 1 个
2025-05-28 18:12:55,585 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:711 - _fix_build_errors - 🔧 修复构建错误: 1 个
2025-05-28 18:12:55,585 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:215 - execute_targeted_fixes - 修复结果: {'success': False, 'message': '修复了 0/1 个构建错误', 'details': [], 'fixed_count': 0}
2025-05-28 18:12:55,587 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:247 - verify_fixes - ✅ 开始验证修复效果...
2025-05-28 18:12:59,036 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "black --check --config pyproject.toml ."
2025-05-28 18:13:02,815 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "flake8 ."
2025-05-28 18:13:06,063 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "python -m py_compile "api_proxy\utils.py""
