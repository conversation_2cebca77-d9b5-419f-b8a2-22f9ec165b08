"""
Aider 集成模块 - 专门处理 Aider AI 功能调用
"""

import logging
import os
import sys
import tempfile
from pathlib import Path
from typing import Dict, Optional, List, Any
import importlib

from bot_agent.utils.llm_retry_handler import enhanced_llm_call, global_retry_handler
from bot_agent.utils.aider_error_handler import global_aider_error_handler

logger = logging.getLogger(__name__)


class AiderIntegration:
    """
    Aider 集成类，负责调用 Aider 的 AI 功能
    """

    def __init__(self):
        self.aider_available = False
        self.coder_class = None
        self.model_class = None
        self.io_class = None
        self._check_aider_availability()

    def _check_aider_availability(self):
        """检查 Aider 是否可用"""
        try:
            # 尝试导入 aider 核心模块
            from aider.coders import Coder
            from aider.models import Model
            from aider.io import InputOutput

            self.coder_class = Coder
            self.model_class = Model
            self.io_class = InputOutput
            self.aider_available = True
            logger.info("Aider 模块导入成功")
        except ImportError as e:
            logger.warning(f"Aider 模块不可用: {e}")
            self.aider_available = False

    async def analyze_project_structure(self, project_path: str = None) -> str:
        """
        使用 Aider AI 分析项目目录结构

        Args:
            project_path: 项目路径，默认为当前目录

        Returns:
            str: AI 分析的项目结构结果
        """
        if not self.aider_available:
            return "很抱歉，Aider AI 功能当前不可用。请确保已正确安装所有依赖。"

        if not project_path:
            project_path = os.getcwd()

        try:
            # 使用 Aider AI 来分析项目结构
            return await self._run_aider_analysis(project_path)

        except Exception as e:
            logger.error(f"使用 Aider AI 分析项目结构时出错: {e}", exc_info=True)
            return f"使用 Aider AI 分析项目结构时出错: {str(e)}"

    async def _run_aider_analysis(self, project_path: str) -> str:
        """
        运行 Aider AI 分析

        Args:
            project_path: 项目路径

        Returns:
            str: AI 分析结果
        """
        try:
            # 创建 AI 模型 (使用环境变量中的模型或默认的OpenRouter模型)
            model_name = os.getenv("AIDER_MODEL", "openrouter/deepseek/deepseek-chat")
            model = self.model_class(model_name)

            # 设置模型温度（如果支持）
            try:
                if hasattr(model, 'temperature'):
                    model.temperature = 0.3
                elif hasattr(model, 'use_temperature'):
                    model.use_temperature = 0.3
            except Exception as e:
                logger.warning(f"无法设置模型温度: {e}")

            # 创建 IO 对象 (非交互模式)
            io = self.io_class(pretty=False, yes=True)

            # 切换到项目目录
            original_cwd = os.getcwd()
            os.chdir(project_path)

            try:
                # 创建 Coder 对象，设置中文语言和积极模式
                chat_language = os.getenv("AIDER_CHAT_LANGUAGE", "Chinese")
                coder = self.coder_class.create(
                    main_model=model,
                    io=io,
                    fnames=[],  # 不指定特定文件，让AI分析整个项目
                    use_git=True,
                    stream=False,
                    verbose=False,
                    chat_language=chat_language,  # 设置AI使用指定语言回复
                    auto_accept_architect=True  # 🚀 自动接受所有编辑建议
                )

                # 构建分析请求 - 明确要求使用中文
                analysis_prompt = """请分析这个项目的目录结构和代码组织。请提供：

1. 项目的主要目录结构概述
2. 主要的源代码目录和文件
3. 配置文件和构建文件
4. 项目的技术栈和架构特点
5. 项目的主要功能模块

重要：请务必用中文回答，并提供详细的分析。所有回复都必须使用中文。"""

                # 运行 AI 分析 - 使用重试机制
                logger.info("开始使用 Aider AI 分析项目结构...")

                # 定义AI调用函数
                async def run_aider_analysis():
                    coder.run(analysis_prompt)
                    if hasattr(coder, 'partial_response_content') and coder.partial_response_content:
                        return coder.partial_response_content
                    else:
                        return None  # 返回None触发重试

                # 使用重试机制执行
                task_info = {
                    "title": "项目结构分析",
                    "task_type": "analysis"
                }

                try:
                    response = await enhanced_llm_call(
                        run_aider_analysis,
                        task_info=task_info
                    )

                    # 如果仍然是降级响应，添加基本项目信息
                    if "AI服务暂时不可用" in response:
                        basic_info = self._generate_basic_project_info(project_path)
                        response = f"{response}\n\n{basic_info}"

                except Exception as e:
                    logger.error(f"AI分析失败，使用基本信息: {e}")
                    response = self._generate_basic_project_info(project_path)

                logger.info("Aider AI 分析完成")
                return response

            finally:
                # 恢复原始工作目录
                os.chdir(original_cwd)

        except Exception as e:
            logger.error(f"运行 Aider AI 分析时出错: {e}", exc_info=True)
            # 如果 AI 分析失败，提供基本的项目信息
            return self._generate_basic_project_info(project_path)

    def _generate_basic_project_info(self, project_path: str) -> str:
        """
        生成基本的项目信息（当 AI 分析失败时的备选方案）

        Args:
            project_path: 项目路径

        Returns:
            str: 基本项目信息
        """
        project_name = Path(project_path).name

        # 简单的文件统计
        file_count = 0
        dir_count = 0
        python_files = 0

        try:
            for item in Path(project_path).rglob('*'):
                if item.is_file():
                    file_count += 1
                    if item.suffix == '.py':
                        python_files += 1
                elif item.is_dir():
                    dir_count += 1
        except Exception as e:
            logger.error(f"统计文件时出错: {e}")

        return f"""# 项目基本信息

**项目名称**: {project_name}
**项目路径**: {project_path}

## 统计信息
- 总文件数: {file_count}
- 目录数: {dir_count}
- Python 文件数: {python_files}

## 说明
由于 Aider AI 分析遇到问题，这里只提供了基本的项目统计信息。
建议检查 Aider 配置和依赖安装情况。

如需详细分析，请确保：
1. 已正确安装所有 Aider 依赖
2. 已配置 AI 模型的 API 密钥
3. 网络连接正常
"""

    async def process_user_request(self, request: str, project_path: str = None) -> str:
        """
        处理用户的通用请求

        Args:
            request: 用户请求内容
            project_path: 项目路径，默认为当前目录

        Returns:
            str: AI 处理结果
        """
        if not self.aider_available:
            return "很抱歉，Aider AI 功能当前不可用。请确保已正确安装所有依赖。"

        if not project_path:
            project_path = os.getcwd()

        try:
            # 使用 Aider AI 来处理用户请求
            return await self._run_aider_request(request, project_path)

        except Exception as e:
            logger.error(f"使用 Aider AI 处理请求时出错: {e}", exc_info=True)
            return f"使用 Aider AI 处理请求时出错: {str(e)}"

    async def _run_aider_request(self, request: str, project_path: str) -> str:
        """
        运行 Aider AI 处理用户请求

        Args:
            request: 用户请求
            project_path: 项目路径

        Returns:
            str: AI 处理结果
        """
        try:
            # 创建 AI 模型 (使用环境变量中的模型或默认的OpenRouter模型)
            model_name = os.getenv("AIDER_MODEL", "openrouter/deepseek/deepseek-chat")
            model = self.model_class(model_name)

            # 设置模型温度（如果支持）
            try:
                if hasattr(model, 'temperature'):
                    model.temperature = 0.3
                elif hasattr(model, 'use_temperature'):
                    model.use_temperature = 0.3
            except Exception as e:
                logger.warning(f"无法设置模型温度: {e}")

            # 创建 IO 对象 (非交互模式)
            io = self.io_class(pretty=False, yes=True)

            # 切换到项目目录
            original_cwd = os.getcwd()
            os.chdir(project_path)

            try:
                # 创建 Coder 对象，设置中文语言和积极模式
                chat_language = os.getenv("AIDER_CHAT_LANGUAGE", "Chinese")
                coder = self.coder_class.create(
                    main_model=model,
                    io=io,
                    fnames=[],
                    use_git=True,
                    stream=False,
                    verbose=False,
                    chat_language=chat_language,  # 设置AI使用指定语言回复
                    auto_accept_architect=True  # 🚀 自动接受所有编辑建议
                )

                # 添加中文语言要求到请求中
                enhanced_request = f"""请用中文处理以下请求：

{request}

重要：请务必用中文回答所有问题和提供所有解释。"""

                # 运行 AI 处理 - 使用重试机制
                logger.info(f"开始使用 Aider AI 处理请求: {request[:50]}...")

                # 定义AI调用函数
                async def run_aider_request():
                    coder.run(enhanced_request)
                    if hasattr(coder, 'partial_response_content') and coder.partial_response_content:
                        return coder.partial_response_content
                    else:
                        return None  # 返回None触发重试

                # 使用重试机制执行
                task_info = {
                    "title": f"用户请求: {request[:50]}...",
                    "task_type": "user_request"
                }

                try:
                    response = await enhanced_llm_call(
                        run_aider_request,
                        task_info=task_info
                    )
                except Exception as e:
                    logger.error(f"AI处理失败: {e}")

                    # 使用错误处理器分析和恢复
                    recovery_result = global_aider_error_handler.auto_recover(
                        str(e),
                        context={
                            "operation": "user_request_processing",
                            "request": request[:100],
                            "project_path": project_path
                        }
                    )

                    if recovery_result["success"]:
                        response = f"已收到您的请求：{request}\n\n遇到了一些问题，但已自动恢复。请稍后再试。\n\n恢复操作：{', '.join(recovery_result['actions_taken'])}"
                    else:
                        response = f"已收到您的请求：{request}\n\n很抱歉，AI 暂时无法生成响应。\n\n错误类型：{recovery_result['error_info']['error_type']}\n建议：{', '.join(recovery_result['error_info']['suggestions'])}"

                logger.info("Aider AI 处理完成")
                return response

            finally:
                # 恢复原始工作目录
                os.chdir(original_cwd)

        except Exception as e:
            logger.error(f"运行 Aider AI 处理请求时出错: {e}", exc_info=True)
            return f"处理请求时出错: {str(e)}"
