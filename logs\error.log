2025-05-28 13:23:04,328 - bot_agent.engines.task_executor - ERROR - task_executor.py:1217 - _handle_job_failure_analysis - 作业失败分析过程出错: asyncio.run() cannot be called from a running event loop
2025-05-28 13:24:55,453 - bot_agent.engines.task_executor - ERROR - task_executor.py:1217 - _handle_job_failure_analysis - 作业失败分析过程出错: asyncio.run() cannot be called from a running event loop
2025-05-28 13:25:41,237 - bot_agent.engines.task_executor - ERROR - task_executor.py:1217 - _handle_job_failure_analysis - 作业失败分析过程出错: asyncio.run() cannot be called from a running event loop
2025-05-28 13:29:09,254 - bot_agent.clients.gitlab_client - ERROR - gitlab_client.py:174 - _make_request - GitLab API request failed (attempt 1/2): 401 Client Error: Unauthorized for url: http://***************/api/v4/projects/9/jobs/776
2025-05-28 13:29:09,255 - bot_agent.clients.gitlab_client - ERROR - gitlab_client.py:181 - _make_request - Error response status code: 401
2025-05-28 13:29:09,255 - bot_agent.clients.gitlab_client - ERROR - gitlab_client.py:185 - _make_request - Error response data: {'message': '401 Unauthorized'}
2025-05-28 13:29:09,390 - bot_agent.clients.gitlab_client - ERROR - gitlab_client.py:494 - get_job_log - 获取作业日志失败: 401 Client Error: Unauthorized for url: http://***************/api/v4/projects/9/jobs/776/trace
2025-05-28 13:29:53,655 - bot_agent.engines.task_executor - ERROR - task_executor.py:1217 - _handle_job_failure_analysis - 作业失败分析过程出错: asyncio.run() cannot be called from a running event loop
2025-05-28 13:31:45,651 - bot_agent.engines.task_executor - ERROR - task_executor.py:1217 - _handle_job_failure_analysis - 作业失败分析过程出错: asyncio.run() cannot be called from a running event loop
2025-05-28 13:33:34,943 - bot_agent.engines.task_executor - ERROR - task_executor.py:1217 - _handle_job_failure_analysis - 作业失败分析过程出错: asyncio.run() cannot be called from a running event loop
2025-05-28 13:33:41,412 - bot_agent.service_registry.consul_client - ERROR - consul_client.py:135 - deregister_service - Failed to deregister service: HTTPConnectionPool(host='***************', port=8500): Max retries exceeded with url: /v1/agent/service/deregister/bot-agent-05b4b9c0 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001BD85F23AD0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-05-28 13:35:08,273 - bot_agent.engines.task_executor - ERROR - task_executor.py:1217 - _handle_job_failure_analysis - 作业失败分析过程出错: asyncio.run() cannot be called from a running event loop
2025-05-28 13:41:37,702 - bot_agent.clients.gitlab_client - ERROR - gitlab_client.py:174 - _make_request - GitLab API request failed (attempt 1/2): 401 Client Error: Unauthorized for url: http://***************/api/v4/projects/9
2025-05-28 13:41:37,702 - bot_agent.clients.gitlab_client - ERROR - gitlab_client.py:181 - _make_request - Error response status code: 401
2025-05-28 13:41:37,703 - bot_agent.clients.gitlab_client - ERROR - gitlab_client.py:185 - _make_request - Error response data: {'message': '401 Unauthorized'}
2025-05-28 13:41:37,704 - bot_agent.engines.task_executor - ERROR - task_executor.py:91 - execute_task - 执行任务 test_job_analysis 时出错: 无法获取项目信息: 9
Traceback (most recent call last):
  File "E:\Projects\aider-plus\bot_agent\engines\task_executor.py", line 68, in execute_task
    project_path = await self._prepare_workspace(task)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Projects\aider-plus\bot_agent\engines\task_executor.py", line 124, in _prepare_workspace
    raise Exception(f"无法获取项目信息: {project_id}")
Exception: 无法获取项目信息: 9
2025-05-28 13:47:24,183 - bot_agent.engines.task_executor - ERROR - task_executor.py:1189 - _handle_job_failure_analysis - 作业失败分析过程出错: asyncio.run() cannot be called from a running event loop
2025-05-28 13:49:22,334 - bot_agent.clients.gitlab_client - ERROR - gitlab_client.py:174 - _make_request - GitLab API request failed (attempt 1/2): 401 Client Error: Unauthorized for url: http://***************/api/v4/projects/9
2025-05-28 13:49:22,334 - bot_agent.clients.gitlab_client - ERROR - gitlab_client.py:181 - _make_request - Error response status code: 401
2025-05-28 13:49:22,334 - bot_agent.clients.gitlab_client - ERROR - gitlab_client.py:185 - _make_request - Error response data: {'message': '401 Unauthorized'}
2025-05-28 13:49:22,336 - bot_agent.engines.task_executor - ERROR - task_executor.py:91 - execute_task - 执行任务 test_thread_job_analysis 时出错: 无法获取项目信息: 9
Traceback (most recent call last):
  File "E:\Projects\aider-plus\bot_agent\engines\task_executor.py", line 68, in execute_task
    project_path = await self._prepare_workspace(task)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Projects\aider-plus\bot_agent\engines\task_executor.py", line 124, in _prepare_workspace
    raise Exception(f"无法获取项目信息: {project_id}")
Exception: 无法获取项目信息: 9
2025-05-28 14:50:27,682 - bot_agent.engines.task_executor - ERROR - task_executor.py:369 - _execute_with_aider - 使用Aider执行任务失败: Coder.__init__() got an unexpected keyword argument 'temperature'
Traceback (most recent call last):
  File "E:\Projects\aider-plus\bot_agent\engines\task_executor.py", line 269, in _execute_with_aider
    coder = Coder.create(
            ^^^^^^^^^^^^^
  File "E:\Projects\aider-plus\aider\coders\base_coder.py", line 181, in create
    res = coder(main_model, io, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: Coder.__init__() got an unexpected keyword argument 'temperature'
2025-05-28 14:50:27,685 - bot_agent.engines.task_executor - ERROR - task_executor.py:91 - execute_task - 执行任务 159e681c-b0f6-4ae1-b7d2-f9e85b61f2ab 时出错: Aider执行失败: Coder.__init__() got an unexpected keyword argument 'temperature'
Traceback (most recent call last):
  File "E:\Projects\aider-plus\bot_agent\engines\task_executor.py", line 269, in _execute_with_aider
    coder = Coder.create(
            ^^^^^^^^^^^^^
  File "E:\Projects\aider-plus\aider\coders\base_coder.py", line 181, in create
    res = coder(main_model, io, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: Coder.__init__() got an unexpected keyword argument 'temperature'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\Projects\aider-plus\bot_agent\engines\task_executor.py", line 71, in execute_task
    result = await self._execute_with_aider(task, project_path)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Projects\aider-plus\bot_agent\engines\task_executor.py", line 370, in _execute_with_aider
    raise Exception(f"Aider执行失败: {str(e)}")
Exception: Aider执行失败: Coder.__init__() got an unexpected keyword argument 'temperature'
2025-05-28 14:58:52,888 - bot_agent.tools.intelligent_tool_coordinator - ERROR - intelligent_tool_coordinator.py:167 - analyze_job_errors - 错误分析失败: 'LogAnalysisTools' object has no attribute 'analyze_job_log'
2025-05-28 15:00:11,976 - bot_agent.tools.intelligent_tool_coordinator - ERROR - intelligent_tool_coordinator.py:167 - analyze_job_errors - 错误分析失败: 'LogAnalysisTools' object has no attribute 'analyze_job_log'
2025-05-28 15:06:25,131 - bot_agent - ERROR - logging_config.py:143 - log_command_execution - 命令执行失败: powershell -Command "flake8 ."
2025-05-28 15:06:25,131 - bot_agent - ERROR - logging_config.py:145 - log_command_execution - 错误信息: Traceback (most recent call last):
  File "<frozen runpy>", line 198, in _run_module_as_main
  File "<frozen runpy>", line 88, in _run_code
  File "E:\Projects\aider-plus\venv\Scripts\flake8.exe\__main__.py", line 7, in <module>
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\main\cli.py", line 23, in main
    app.run(argv)
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\main\application.py", line 198, in run
    self._run(argv)
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\main\application.py", line 186, in _run
    self.initialize(argv)
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\main\application.py", line 165, in initialize
    self.plugins, self.options = parse_args(argv)
                                 ^^^^^^^^^^^^^^^^
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\options\parse_args.py", line 53, in parse_args
    opts = aggregator.aggregate_options(option_manager, cfg, cfg_dir, rest)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\options\aggregator.py", line 30, in aggregate_options
    parsed_config = config.parse_config(manager, cfg, cfg_dir)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\options\config.py", line 131, in parse_config
    raise ValueError(
ValueError: Error code '#' supplied to 'extend-ignore' option does not match '^[A-Z]{1,3}[0-9]{0,3}$'

2025-05-28 15:06:28,023 - bot_agent - ERROR - logging_config.py:143 - log_command_execution - 命令执行失败: powershell -Command "python -m py_compile *.py"
2025-05-28 15:06:28,023 - bot_agent - ERROR - logging_config.py:145 - log_command_execution - 错误信息: [Errno 22] Invalid argument: '*.py'
2025-05-28 15:09:16,273 - bot_agent.tools.intelligent_tool_coordinator - ERROR - intelligent_tool_coordinator.py:276 - verify_fixes - 验证失败: 'TestingTools' object has no attribute 'run_tests'
2025-05-28 15:09:51,001 - bot_agent - ERROR - logging_config.py:143 - log_command_execution - 命令执行失败: powershell -Command "flake8 ."
2025-05-28 15:09:51,001 - bot_agent - ERROR - logging_config.py:145 - log_command_execution - 错误信息: Traceback (most recent call last):
  File "<frozen runpy>", line 198, in _run_module_as_main
  File "<frozen runpy>", line 88, in _run_code
  File "E:\Projects\aider-plus\venv\Scripts\flake8.exe\__main__.py", line 7, in <module>
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\main\cli.py", line 23, in main
    app.run(argv)
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\main\application.py", line 198, in run
    self._run(argv)
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\main\application.py", line 186, in _run
    self.initialize(argv)
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\main\application.py", line 165, in initialize
    self.plugins, self.options = parse_args(argv)
                                 ^^^^^^^^^^^^^^^^
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\options\parse_args.py", line 53, in parse_args
    opts = aggregator.aggregate_options(option_manager, cfg, cfg_dir, rest)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\options\aggregator.py", line 30, in aggregate_options
    parsed_config = config.parse_config(manager, cfg, cfg_dir)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\options\config.py", line 131, in parse_config
    raise ValueError(
ValueError: Error code '#' supplied to 'extend-ignore' option does not match '^[A-Z]{1,3}[0-9]{0,3}$'

2025-05-28 15:09:53,949 - bot_agent - ERROR - logging_config.py:143 - log_command_execution - 命令执行失败: powershell -Command "python -m py_compile *.py"
2025-05-28 15:09:53,949 - bot_agent - ERROR - logging_config.py:145 - log_command_execution - 错误信息: [Errno 22] Invalid argument: '*.py'
2025-05-28 15:22:33,340 - bot_agent.engines.ai_solution_executor - ERROR - ai_solution_executor.py:201 - execute_actions - ❌ 操作失败: 执行Python脚本 - 
2025-05-28 15:22:33,342 - bot_agent.engines.ai_solution_executor - ERROR - ai_solution_executor.py:201 - execute_actions - ❌ 操作失败: 执行Python脚本 - 
2025-05-28 15:22:33,344 - bot_agent.engines.ai_solution_executor - ERROR - ai_solution_executor.py:201 - execute_actions - ❌ 操作失败: 执行Python脚本 - 
2025-05-28 15:22:33,345 - bot_agent.engines.ai_solution_executor - ERROR - ai_solution_executor.py:201 - execute_actions - ❌ 操作失败: 执行修复命令: {missing_package}", - 
2025-05-28 15:22:33,346 - bot_agent.engines.ai_solution_executor - ERROR - ai_solution_executor.py:201 - execute_actions - ❌ 操作失败: 执行修复命令: {missing_package}\n建议: - 
2025-05-28 16:46:38,044 - bot_agent - ERROR - logging_config.py:143 - log_command_execution - 命令执行失败: powershell -Command "flake8 ."
2025-05-28 16:46:38,045 - bot_agent - ERROR - logging_config.py:145 - log_command_execution - 错误信息: Traceback (most recent call last):
  File "<frozen runpy>", line 198, in _run_module_as_main
  File "<frozen runpy>", line 88, in _run_code
  File "E:\Projects\aider-plus\venv\Scripts\flake8.exe\__main__.py", line 7, in <module>
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\main\cli.py", line 23, in main
    app.run(argv)
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\main\application.py", line 198, in run
    self._run(argv)
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\main\application.py", line 186, in _run
    self.initialize(argv)
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\main\application.py", line 165, in initialize
    self.plugins, self.options = parse_args(argv)
                                 ^^^^^^^^^^^^^^^^
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\options\parse_args.py", line 53, in parse_args
    opts = aggregator.aggregate_options(option_manager, cfg, cfg_dir, rest)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\options\aggregator.py", line 30, in aggregate_options
    parsed_config = config.parse_config(manager, cfg, cfg_dir)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\options\config.py", line 131, in parse_config
    raise ValueError(
ValueError: Error code '#' supplied to 'extend-ignore' option does not match '^[A-Z]{1,3}[0-9]{0,3}$'

2025-05-28 16:46:40,958 - bot_agent - ERROR - logging_config.py:143 - log_command_execution - 命令执行失败: powershell -Command "python -m py_compile *.py"
2025-05-28 16:46:40,959 - bot_agent - ERROR - logging_config.py:145 - log_command_execution - 错误信息: [Errno 22] Invalid argument: '*.py'
2025-05-28 16:47:48,676 - bot_agent.tools.intelligent_tool_coordinator - ERROR - intelligent_tool_coordinator.py:276 - verify_fixes - 验证失败: 'TestingTools' object has no attribute 'run_tests'
2025-05-28 16:48:45,461 - bot_agent - ERROR - logging_config.py:143 - log_command_execution - 命令执行失败: powershell -Command "flake8 ."
2025-05-28 16:48:45,461 - bot_agent - ERROR - logging_config.py:145 - log_command_execution - 错误信息: Traceback (most recent call last):
  File "<frozen runpy>", line 198, in _run_module_as_main
  File "<frozen runpy>", line 88, in _run_code
  File "E:\Projects\aider-plus\venv\Scripts\flake8.exe\__main__.py", line 7, in <module>
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\main\cli.py", line 23, in main
    app.run(argv)
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\main\application.py", line 198, in run
    self._run(argv)
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\main\application.py", line 186, in _run
    self.initialize(argv)
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\main\application.py", line 165, in initialize
    self.plugins, self.options = parse_args(argv)
                                 ^^^^^^^^^^^^^^^^
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\options\parse_args.py", line 53, in parse_args
    opts = aggregator.aggregate_options(option_manager, cfg, cfg_dir, rest)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\options\aggregator.py", line 30, in aggregate_options
    parsed_config = config.parse_config(manager, cfg, cfg_dir)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\options\config.py", line 131, in parse_config
    raise ValueError(
ValueError: Error code '#' supplied to 'extend-ignore' option does not match '^[A-Z]{1,3}[0-9]{0,3}$'

2025-05-28 16:48:48,325 - bot_agent - ERROR - logging_config.py:143 - log_command_execution - 命令执行失败: powershell -Command "python -m py_compile *.py"
2025-05-28 16:48:48,325 - bot_agent - ERROR - logging_config.py:145 - log_command_execution - 错误信息: [Errno 22] Invalid argument: '*.py'
2025-05-28 17:02:56,605 - bot_agent - ERROR - logging_config.py:143 - log_command_execution - 命令执行失败: powershell -Command "flake8 ."
2025-05-28 17:02:56,606 - bot_agent - ERROR - logging_config.py:145 - log_command_execution - 错误信息: Traceback (most recent call last):
  File "<frozen runpy>", line 198, in _run_module_as_main
  File "<frozen runpy>", line 88, in _run_code
  File "E:\Projects\aider-plus\venv\Scripts\flake8.exe\__main__.py", line 7, in <module>
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\main\cli.py", line 23, in main
    app.run(argv)
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\main\application.py", line 198, in run
    self._run(argv)
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\main\application.py", line 186, in _run
    self.initialize(argv)
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\main\application.py", line 165, in initialize
    self.plugins, self.options = parse_args(argv)
                                 ^^^^^^^^^^^^^^^^
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\options\parse_args.py", line 53, in parse_args
    opts = aggregator.aggregate_options(option_manager, cfg, cfg_dir, rest)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\options\aggregator.py", line 30, in aggregate_options
    parsed_config = config.parse_config(manager, cfg, cfg_dir)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\options\config.py", line 131, in parse_config
    raise ValueError(
ValueError: Error code '#' supplied to 'extend-ignore' option does not match '^[A-Z]{1,3}[0-9]{0,3}$'

2025-05-28 17:02:59,559 - bot_agent - ERROR - logging_config.py:143 - log_command_execution - 命令执行失败: powershell -Command "python -m py_compile *.py"
2025-05-28 17:02:59,560 - bot_agent - ERROR - logging_config.py:145 - log_command_execution - 错误信息: [Errno 22] Invalid argument: '*.py'
2025-05-28 17:04:20,400 - bot_agent - ERROR - logging_config.py:143 - log_command_execution - 命令执行失败: powershell -Command "python -m pytest -v --tb=short --json-report --json-report-file=test_results.json test_*.py"
2025-05-28 17:04:20,401 - bot_agent - ERROR - logging_config.py:145 - log_command_execution - 错误信息: ERROR: usage: __main__.py [options] [file_or_dir] [file_or_dir] [...]
__main__.py: error: unrecognized arguments: --json-report --json-report-file=test_results.json
  inifile: E:\aider-git-repos\ai-proxy\pyproject.toml
  rootdir: E:\aider-git-repos\ai-proxy


2025-05-28 17:04:22,939 - bot_agent - ERROR - logging_config.py:143 - log_command_execution - 命令执行失败: powershell -Command "python -m py_compile *.py"
2025-05-28 17:04:22,940 - bot_agent - ERROR - logging_config.py:145 - log_command_execution - 错误信息: [Errno 22] Invalid argument: '*.py'
2025-05-28 17:18:30,234 - bot_agent.tools.intelligent_tool_coordinator - ERROR - intelligent_tool_coordinator.py:167 - analyze_job_errors - 错误分析失败: unhashable type: 'dict'
2025-05-28 17:20:17,497 - bot_agent.tools.intelligent_tool_coordinator - ERROR - intelligent_tool_coordinator.py:560 - _fix_dependency_errors - 修复依赖错误失败: 'dict' object has no attribute 'lower'
2025-05-28 17:20:17,499 - bot_agent.tools.intelligent_tool_coordinator - ERROR - intelligent_tool_coordinator.py:607 - _fix_build_errors - 修复构建错误失败: 'dict' object has no attribute 'lower'
2025-05-28 17:29:55,747 - bot_agent - ERROR - logging_config.py:143 - log_command_execution - 命令执行失败: powershell -Command "python -m pytest -v --tb=short --json-report --json-report-file=test_results.json test_*.py"
2025-05-28 17:29:55,748 - bot_agent - ERROR - logging_config.py:145 - log_command_execution - 错误信息: ERROR: usage: __main__.py [options] [file_or_dir] [file_or_dir] [...]
__main__.py: error: unrecognized arguments: --json-report --json-report-file=test_results.json
  inifile: None
  rootdir: C:\Users\<USER>\AppData\Local\Temp\tmpgak5k3ha


2025-05-28 17:29:58,832 - bot_agent - ERROR - logging_config.py:143 - log_command_execution - 命令执行失败: powershell -Command "python -m py_compile *.py"
2025-05-28 17:29:58,832 - bot_agent - ERROR - logging_config.py:145 - log_command_execution - 错误信息: [Errno 22] Invalid argument: '*.py'
2025-05-28 17:32:53,250 - bot_agent - ERROR - logging_config.py:143 - log_command_execution - 命令执行失败: powershell -Command "python -m pytest -v --tb=short --json-report --json-report-file=test_results.json test_*.py"
2025-05-28 17:32:53,251 - bot_agent - ERROR - logging_config.py:145 - log_command_execution - 错误信息: ERROR: usage: __main__.py [options] [file_or_dir] [file_or_dir] [...]
__main__.py: error: unrecognized arguments: --json-report --json-report-file=test_results.json
  inifile: None
  rootdir: C:\Users\<USER>\AppData\Local\Temp\tmpk3t1dkv7


2025-05-28 17:33:00,350 - bot_agent - ERROR - logging_config.py:143 - log_command_execution - 命令执行失败: powershell -Command "python -m pytest -v --tb=short --json-report --json-report-file=test_results.json test_*.py"
2025-05-28 17:33:00,351 - bot_agent - ERROR - logging_config.py:145 - log_command_execution - 错误信息: ERROR: usage: __main__.py [options] [file_or_dir] [file_or_dir] [...]
__main__.py: error: unrecognized arguments: --json-report --json-report-file=test_results.json
  inifile: None
  rootdir: C:\Users\<USER>\AppData\Local\Temp\tmprx4l1tov


2025-05-28 17:39:43,373 - bot_agent - ERROR - logging_config.py:143 - log_command_execution - 命令执行失败: powershell -Command "flake8 ."
2025-05-28 17:39:43,374 - bot_agent - ERROR - logging_config.py:145 - log_command_execution - 错误信息: Traceback (most recent call last):
  File "<frozen runpy>", line 198, in _run_module_as_main
  File "<frozen runpy>", line 88, in _run_code
  File "E:\Projects\aider-plus\venv\Scripts\flake8.exe\__main__.py", line 7, in <module>
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\main\cli.py", line 23, in main
    app.run(argv)
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\main\application.py", line 198, in run
    self._run(argv)
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\main\application.py", line 186, in _run
    self.initialize(argv)
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\main\application.py", line 165, in initialize
    self.plugins, self.options = parse_args(argv)
                                 ^^^^^^^^^^^^^^^^
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\options\parse_args.py", line 53, in parse_args
    opts = aggregator.aggregate_options(option_manager, cfg, cfg_dir, rest)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\options\aggregator.py", line 30, in aggregate_options
    parsed_config = config.parse_config(manager, cfg, cfg_dir)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\options\config.py", line 131, in parse_config
    raise ValueError(
ValueError: Error code '#' supplied to 'extend-ignore' option does not match '^[A-Z]{1,3}[0-9]{0,3}$'

