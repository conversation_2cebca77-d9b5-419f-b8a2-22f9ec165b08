2025-05-28 19:21:57,032 - bot_agent.tools.intelligent_tool_coordinator - ERROR - intelligent_tool_coordinator.py:376 - _ai_generate_fix_plan - AI修复方案生成异常: 'str' object has no attribute 'get'
2025-05-28 19:58:29,441 - bot_agent.tools.intelligent_tool_coordinator - ERROR - intelligent_tool_coordinator.py:111 - get_job_info_and_log - 获取作业信息和日志失败: ToolResult.__init__() got an unexpected keyword argument 'tool_name'
2025-05-28 19:58:29,442 - bot_agent.tools.intelligent_tool_coordinator - ERROR - intelligent_tool_coordinator.py:1249 - get_job_info_and_log - 同步获取作业信息失败: ToolResult.__init__() got an unexpected keyword argument 'tool_name'
2025-05-28 19:58:29,442 - bot_agent.engines.task_executor - ERROR - task_executor.py:1276 - _handle_job_failure_analysis - 作业失败分析过程出错: ToolResult.__init__() got an unexpected keyword argument 'tool_name'
2025-05-28 20:10:33,386 - bot_agent.tools.intelligent_tool_coordinator - ERROR - intelligent_tool_coordinator.py:1261 - execute_targeted_fixes - 同步执行修复失败: 
2025-05-28 20:10:41,492 - bot_agent - ERROR - logging_config.py:143 - log_command_execution - 命令执行失败: powershell -Command "flake8 ."
2025-05-28 20:10:41,492 - bot_agent - ERROR - logging_config.py:145 - log_command_execution - 错误信息: Traceback (most recent call last):
  File "<frozen runpy>", line 198, in _run_module_as_main
  File "<frozen runpy>", line 88, in _run_code
  File "E:\Projects\aider-plus\venv\Scripts\flake8.exe\__main__.py", line 7, in <module>
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\main\cli.py", line 23, in main
    app.run(argv)
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\main\application.py", line 198, in run
    self._run(argv)
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\main\application.py", line 186, in _run
    self.initialize(argv)
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\main\application.py", line 165, in initialize
    self.plugins, self.options = parse_args(argv)
                                 ^^^^^^^^^^^^^^^^
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\options\parse_args.py", line 53, in parse_args
    opts = aggregator.aggregate_options(option_manager, cfg, cfg_dir, rest)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\options\aggregator.py", line 30, in aggregate_options
    parsed_config = config.parse_config(manager, cfg, cfg_dir)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\options\config.py", line 131, in parse_config
    raise ValueError(
ValueError: Error code '#' supplied to 'extend-ignore' option does not match '^[A-Z]{1,3}[0-9]{0,3}$'

2025-05-28 20:17:59,588 - bot_agent.tools.intelligent_tool_coordinator - ERROR - intelligent_tool_coordinator.py:1261 - execute_targeted_fixes - 同步执行修复失败: 
2025-05-28 20:18:03,887 - bot_agent - ERROR - logging_config.py:143 - log_command_execution - 命令执行失败: powershell -Command "python -m pytest -v --tb=short --json-report --json-report-file=test_results.json test_*.py"
2025-05-28 20:18:03,888 - bot_agent - ERROR - logging_config.py:145 - log_command_execution - 错误信息: ERROR: usage: __main__.py [options] [file_or_dir] [file_or_dir] [...]
__main__.py: error: unrecognized arguments: --json-report --json-report-file=test_results.json
  inifile: E:\aider-git-repos\ai-proxy\pyproject.toml
  rootdir: E:\aider-git-repos\ai-proxy


2025-05-28 20:19:23,839 - bot_agent - ERROR - logging_config.py:143 - log_command_execution - 命令执行失败: powershell -Command "检查api_proxy/job_analysis.py中是否有'class JobAnalysis'定义，检查api_proxy/utils.py中是否有'def redact_sensitive_data'，并在api_proxy/__init__.py中添加'from .job_analysis import JobAnalysis'和'from .utils import redact_sensitive_data'"
2025-05-28 20:19:23,839 - bot_agent - ERROR - logging_config.py:145 - log_command_execution - 错误信息: 检查api_proxy/job_analysis.py中是否有class JobAnalysis定义，检查api_proxy/utils.py中是否有def redact_sensitive_
data，并在api_proxy/__init__.py中添加from .job_analysis import JobAnalysis和from .utils import redact_sensitive
_data : 无法将“检查api_proxy/job_analysis.py中是否有class JobAnalysis定义，检查api_proxy/utils.py中是否有def r
edact_sensitive_data，并在api_proxy/__init__.py中添加from .job_analysis import JobAnalysis和from .utils import 
redact_sensitive_data”项识别为 cmdlet、函数、脚本文件或可运行程序的名称。请检查名称的拼写，如果包括路径，请确
保路径正确，然后再试一次。
所在位置 行:1 字符: 1
+ 检查api_proxy/job_analysis.py中是否有'class JobAnalysis'定义，检查api_proxy/util ...
+ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : ObjectNotFound: (检查api_proxy/job..._sensitive_data:String) [], CommandNotFoundE
x    ception
    + FullyQualifiedErrorId : CommandNotFoundException
 

2025-05-28 20:19:27,304 - bot_agent - ERROR - logging_config.py:143 - log_command_execution - 命令执行失败: powershell -Command "在定义JobErrorType的模块（如api_proxy/errors.py）中导出，并在测试文件中添加'from api_proxy.errors import JobErrorType'"
2025-05-28 20:19:27,304 - bot_agent - ERROR - logging_config.py:145 - log_command_execution - 错误信息: 在定义JobErrorType的模块（如api_proxy/errors.py）中导出，并在测试文件中添加from api_proxy.errors import JobErro
rType : 无法将“在定义JobErrorType的模块（如api_proxy/errors.py）中导出，并在测试文件中添加from api_proxy.error
s import JobErrorType”项识别为 cmdlet、函数、脚本文件或可运行程序的名称。请检查名称的拼写，如果包括路径，请确
保路径正确，然后再试一次。
所在位置 行:1 字符: 1
+ 在定义JobErrorType的模块（如api_proxy/errors.py）中导出，并在测试文件中添加'from api_proxy. ...
+ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : ObjectNotFound: (在定义JobErrorType...rt JobErrorType:String) [], CommandNotFound
Ex    ception
    + FullyQualifiedErrorId : CommandNotFoundException
 

2025-05-28 20:19:30,629 - bot_agent - ERROR - logging_config.py:143 - log_command_execution - 命令执行失败: powershell -Command "将测试文件中的导入语句改为绝对路径，例如'from api_proxy.job_analysis import JobAnalysis'"
2025-05-28 20:19:30,630 - bot_agent - ERROR - logging_config.py:145 - log_command_execution - 错误信息: 将测试文件中的导入语句改为绝对路径，例如from api_proxy.job_analysis import JobAnalysis : 无法将“将测试文件中的
导入语句改为绝对路径，例如from api_proxy.job_analysis import JobAnalysis”项识别为 cmdlet、函数、脚本文件或可运
行程序的名称。请检查名称的拼写，如果包括路径，请确保路径正确，然后再试一次。
所在位置 行:1 字符: 1
+ 将测试文件中的导入语句改为绝对路径，例如'from api_proxy.job_analysis import JobAnalysis'
+ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : ObjectNotFound: (将测试文件中的导入语句改为绝对...ort JobAnalysis:String) [], Com
mandNotFoundEx    ception
    + FullyQualifiedErrorId : CommandNotFoundException
 

2025-05-28 20:19:35,537 - bot_agent - ERROR - logging_config.py:143 - log_command_execution - 命令执行失败: powershell -Command "python -m pytest tests/ -v"
