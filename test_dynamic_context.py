#!/usr/bin/env python3
"""
测试项目上下文是否真的是动态获取的
"""

import sys
import os
import asyncio
import tempfile
import shutil

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_different_project_paths():
    """测试不同项目路径是否会产生不同的上下文"""
    print("🔍 测试不同项目路径的上下文收集...")
    
    try:
        from bot_agent.tools.project_context_collector import project_context_collector
        
        # 测试1: 当前项目（aider-plus）
        current_project = os.getcwd()
        print(f"📁 测试项目1: {current_project}")
        
        context1 = await project_context_collector.collect_full_context(current_project)
        project_info1 = context1.get('project_type', {})
        
        print(f"  项目名称: {project_info1.get('name', 'unknown')}")
        print(f"  项目类型: {project_info1.get('type', 'unknown')}")
        print(f"  编程语言: {project_info1.get('language', 'unknown')}")
        
        # 测试2: 创建一个临时的Node.js项目
        with tempfile.TemporaryDirectory() as temp_dir:
            print(f"\n📁 测试项目2: {temp_dir}")
            
            # 创建package.json文件
            package_json = {
                "name": "test-nodejs-project",
                "version": "1.0.0",
                "dependencies": {
                    "express": "^4.18.0",
                    "react": "^18.0.0"
                }
            }
            
            import json
            with open(os.path.join(temp_dir, 'package.json'), 'w') as f:
                json.dump(package_json, f, indent=2)
            
            context2 = await project_context_collector.collect_full_context(temp_dir)
            project_info2 = context2.get('project_type', {})
            
            print(f"  项目名称: {project_info2.get('name', 'unknown')}")
            print(f"  项目类型: {project_info2.get('type', 'unknown')}")
            print(f"  编程语言: {project_info2.get('language', 'unknown')}")
        
        # 测试3: 创建一个临时的Go项目
        with tempfile.TemporaryDirectory() as temp_dir:
            print(f"\n📁 测试项目3: {temp_dir}")
            
            # 创建go.mod文件
            go_mod_content = """module test-go-project

go 1.19

require (
    github.com/gin-gonic/gin v1.9.0
)
"""
            with open(os.path.join(temp_dir, 'go.mod'), 'w') as f:
                f.write(go_mod_content)
            
            context3 = await project_context_collector.collect_full_context(temp_dir)
            project_info3 = context3.get('project_type', {})
            
            print(f"  项目名称: {project_info3.get('name', 'unknown')}")
            print(f"  项目类型: {project_info3.get('type', 'unknown')}")
            print(f"  编程语言: {project_info3.get('language', 'unknown')}")
        
        # 验证结果
        print("\n📊 验证结果:")
        
        # 检查项目名称是否不同
        name1 = project_info1.get('name', '')
        name2 = project_info2.get('name', '')
        name3 = project_info3.get('name', '')
        
        if name1 != name2 and name2 != name3 and name1 != name3:
            print("✅ 项目名称动态获取正确")
        else:
            print(f"❌ 项目名称可能是写死的: {name1}, {name2}, {name3}")
            return False
        
        # 检查项目类型是否正确识别
        type1 = project_info1.get('type', '')
        type2 = project_info2.get('type', '')
        type3 = project_info3.get('type', '')
        
        if type1 == 'python' and type2 == 'nodejs' and type3 == 'go':
            print("✅ 项目类型动态识别正确")
        else:
            print(f"❌ 项目类型识别错误: {type1}, {type2}, {type3}")
            return False
        
        # 检查编程语言是否正确识别
        lang1 = project_info1.get('language', '')
        lang2 = project_info2.get('language', '')
        lang3 = project_info3.get('language', '')
        
        if lang1 == 'python' and lang2 == 'javascript' and lang3 == 'go':
            print("✅ 编程语言动态识别正确")
        else:
            print(f"❌ 编程语言识别错误: {lang1}, {lang2}, {lang3}")
            return False
        
        print("🎉 所有项目信息都是动态获取的！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_git_info_dynamic():
    """测试Git信息是否动态获取"""
    print("\n🔍 测试Git信息动态获取...")
    
    try:
        from bot_agent.tools.project_context_collector import project_context_collector
        
        # 测试当前项目的Git信息
        current_project = os.getcwd()
        context = await project_context_collector.collect_full_context(current_project)
        git_info = context.get('git_info', {})
        
        print(f"📋 Git信息:")
        print(f"  当前分支: {git_info.get('branch', 'unknown')}")
        print(f"  最近提交数量: {len(git_info.get('recent_commits', []))}")
        
        # 检查是否获取到真实的Git信息
        if git_info.get('branch') and git_info.get('recent_commits'):
            print("✅ Git信息动态获取成功")
            return True
        else:
            print("❌ Git信息获取失败或为空")
            return False
        
    except Exception as e:
        print(f"❌ Git信息测试失败: {e}")
        return False

async def test_environment_info_dynamic():
    """测试环境信息是否动态获取"""
    print("\n🔍 测试环境信息动态获取...")
    
    try:
        from bot_agent.tools.project_context_collector import project_context_collector
        
        # 获取环境信息
        current_project = os.getcwd()
        context = await project_context_collector.collect_full_context(current_project)
        env_info = context.get('environment_info', {})
        
        # 处理ToolResult对象
        if hasattr(env_info, 'success') and env_info.success:
            env_data = env_info.data if hasattr(env_info, 'data') else {}
        elif isinstance(env_info, dict) and env_info.get('success'):
            env_data = env_info.get('data', {})
        else:
            env_data = {}
        
        print(f"📋 环境信息:")
        print(f"  操作系统: {env_data.get('system', 'unknown')}")
        print(f"  Python版本: {env_data.get('python_version', 'unknown')}")
        print(f"  平台: {env_data.get('platform', 'unknown')}")
        
        # 验证是否获取到真实的环境信息
        import platform
        real_system = platform.system()
        real_python_version = platform.python_version()
        
        detected_system = env_data.get('system', '')
        detected_python = env_data.get('python_version', '')
        
        if real_system in detected_system and real_python_version in detected_python:
            print("✅ 环境信息动态获取正确")
            return True
        else:
            print(f"❌ 环境信息不匹配:")
            print(f"  实际系统: {real_system}, 检测到: {detected_system}")
            print(f"  实际Python: {real_python_version}, 检测到: {detected_python}")
            return False
        
    except Exception as e:
        print(f"❌ 环境信息测试失败: {e}")
        return False

async def test_project_structure_dynamic():
    """测试项目结构是否动态获取"""
    print("\n🔍 测试项目结构动态获取...")
    
    try:
        from bot_agent.tools.project_context_collector import project_context_collector
        
        # 测试1: 当前项目
        current_project = os.getcwd()
        context1 = await project_context_collector.collect_full_context(current_project)
        struct_info1 = context1.get('project_structure', {})
        
        # 测试2: 创建一个小的临时项目
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建几个文件
            with open(os.path.join(temp_dir, 'test1.py'), 'w') as f:
                f.write('print("hello")')
            with open(os.path.join(temp_dir, 'test2.py'), 'w') as f:
                f.write('print("world")')
            
            context2 = await project_context_collector.collect_full_context(temp_dir)
            struct_info2 = context2.get('project_structure', {})
        
        print(f"📋 项目结构对比:")
        print(f"  当前项目: {struct_info1.get('summary', 'unknown')}")
        print(f"  临时项目: {struct_info2.get('summary', 'unknown')}")
        
        # 验证结构信息是否不同
        summary1 = struct_info1.get('summary', '')
        summary2 = struct_info2.get('summary', '')
        
        if summary1 != summary2 and '2 个文件' in summary2:
            print("✅ 项目结构动态获取正确")
            return True
        else:
            print(f"❌ 项目结构可能不是动态的: {summary1} vs {summary2}")
            return False
        
    except Exception as e:
        print(f"❌ 项目结构测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🧪 项目上下文动态获取验证测试")
    print("=" * 60)
    
    tests = [
        ("不同项目路径", test_different_project_paths),
        ("Git信息动态获取", test_git_info_dynamic),
        ("环境信息动态获取", test_environment_info_dynamic),
        ("项目结构动态获取", test_project_structure_dynamic),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔬 执行 {test_name}测试...")
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            import traceback
            traceback.print_exc()
            results.append((test_name, False))
    
    # 汇总结果
    print("\n📊 测试结果汇总:")
    print("-" * 40)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！项目上下文信息完全是动态获取的。")
        print("\n💡 验证结果：")
        print("1. ✅ 项目名称根据实际目录名动态获取")
        print("2. ✅ 项目类型根据配置文件动态识别")
        print("3. ✅ 编程语言根据项目特征动态判断")
        print("4. ✅ Git信息实时从仓库获取")
        print("5. ✅ 环境信息实时从系统获取")
        print("6. ✅ 项目结构实时扫描目录获取")
        print("\n🎯 结论：")
        print("- 🟢 没有任何硬编码的项目信息")
        print("- 🟢 所有信息都根据实际工作目录动态获取")
        print("- 🟢 不同项目会产生完全不同的上下文")
        print("- 🟢 系统具备真正的项目感知能力")
        print("\n🚀 这意味着AI能够为任何项目生成准确的修复方案！")
    else:
        print("⚠️ 部分测试失败，可能存在硬编码信息。")
    
    return passed == total

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
