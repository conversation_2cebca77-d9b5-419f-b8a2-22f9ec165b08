"""
对话分析Web界面
基于FastAPI的对话记录查看和分析系统
"""

import os
import sys
import re
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Any, Optional

from fastapi import FastAPI, Request, HTTPException, Query
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
import uvicorn

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from bot_agent.utils.conversation_logger import ConversationLogger
from .error_log_collector import global_error_collector

app = FastAPI(title="Aider行为分析系统", description="Aider AI编程助手行为分析和优化平台")

# 设置模板和静态文件
templates = Jinja2Templates(directory="web_ui/templates")
app.mount("/static", StaticFiles(directory="web_ui/static"), name="static")

# 初始化对话记录器
conversation_logger = ConversationLogger()


@app.get("/", response_class=HTMLResponse)
async def dashboard(request: Request):
    """主仪表板"""
    return templates.TemplateResponse("dashboard.html", {"request": request})


@app.get("/api/sessions")
async def get_sessions(
    task_type: Optional[str] = Query(None, description="任务类型过滤"),
    status: Optional[str] = Query(None, description="状态过滤"),
    date_from: Optional[str] = Query(None, description="开始日期"),
    date_to: Optional[str] = Query(None, description="结束日期"),
    limit: int = Query(50, description="返回数量限制")
):
    """获取会话列表API"""
    try:
        sessions = conversation_logger.search_sessions(
            task_type=task_type,
            status=status,
            date_from=date_from,
            date_to=date_to
        )

        # 限制返回数量
        sessions = sessions[:limit]

        return JSONResponse({
            "success": True,
            "data": sessions,
            "total": len(sessions)
        })
    except Exception as e:
        return JSONResponse({
            "success": False,
            "error": str(e)
        }, status_code=500)


@app.get("/api/sessions/{session_id}")
async def get_session_detail(session_id: str):
    """获取会话详情API"""
    try:
        session = conversation_logger._load_session_from_file(session_id)
        if not session:
            raise HTTPException(status_code=404, detail="会话未找到")

        # 转换为字典格式
        session_dict = {
            "session_id": session.session_id,
            "task_id": session.task_id,
            "task_title": session.task_title,
            "task_type": session.task_type,
            "project_path": session.project_path,
            "started_at": session.started_at,
            "ended_at": session.ended_at,
            "total_duration": session.total_duration,
            "status": session.status if isinstance(session.status, str) else session.status.value,
            "final_result": session.final_result,
            "error_summary": session.error_summary,
            "metadata": session.metadata,
            "rounds": []
        }

        # 转换轮次数据
        for round_info in session.rounds:
            round_dict = {
                "round_number": round_info.round_number,
                "round_name": round_info.round_name,
                "prompt": round_info.prompt,
                "response": round_info.response,
                "model_name": round_info.model_name,
                "timestamp": round_info.timestamp,
                "duration": round_info.duration,
                "status": round_info.status if isinstance(round_info.status, str) else round_info.status.value,
                "retry_count": round_info.retry_count,
                "error_message": round_info.error_message,
                "token_usage": round_info.token_usage,
                "model_config": round_info.model_config
            }
            session_dict["rounds"].append(round_dict)

        return JSONResponse({
            "success": True,
            "data": session_dict
        })
    except Exception as e:
        return JSONResponse({
            "success": False,
            "error": str(e)
        }, status_code=500)


@app.get("/api/statistics")
async def get_statistics(
    days: int = Query(7, description="统计天数")
):
    """获取统计信息API"""
    try:
        # 计算日期范围
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)

        date_from = start_date.strftime("%Y-%m-%d")
        date_to = end_date.strftime("%Y-%m-%d")

        # 获取会话数据
        sessions = conversation_logger.search_sessions(
            date_from=date_from,
            date_to=date_to
        )

        # 计算统计信息
        total_sessions = len(sessions)
        success_sessions = len([s for s in sessions if s['status'] == 'success'])
        failed_sessions = len([s for s in sessions if s['status'] == 'failed'])
        in_progress_sessions = len([s for s in sessions if s['status'] == 'in_progress'])

        # 按任务类型统计
        task_types = {}
        for session in sessions:
            task_type = session['task_type']
            if task_type not in task_types:
                task_types[task_type] = {'total': 0, 'success': 0, 'failed': 0}
            task_types[task_type]['total'] += 1
            if session['status'] == 'success':
                task_types[task_type]['success'] += 1
            elif session['status'] == 'failed':
                task_types[task_type]['failed'] += 1

        # 按日期统计
        daily_stats = {}
        for session in sessions:
            date = session['started_at'][:10]  # YYYY-MM-DD
            if date not in daily_stats:
                daily_stats[date] = {'total': 0, 'success': 0, 'failed': 0}
            daily_stats[date]['total'] += 1
            if session['status'] == 'success':
                daily_stats[date]['success'] += 1
            elif session['status'] == 'failed':
                daily_stats[date]['failed'] += 1

        # 平均轮次统计
        total_rounds = sum(session['rounds_count'] for session in sessions)
        avg_rounds = total_rounds / total_sessions if total_sessions > 0 else 0

        # 执行时长分布统计
        duration_distribution = {
            "0-30秒": 0,
            "30秒-2分钟": 0,
            "2-5分钟": 0,
            "5-10分钟": 0,
            "10分钟以上": 0
        }

        total_duration = 0
        sessions_with_duration = 0

        for session in sessions:
            # 计算会话持续时间
            if session.get('ended_at') and session.get('started_at'):
                try:
                    # 处理时间格式
                    started_at = session['started_at']
                    ended_at = session['ended_at']

                    # 处理不同的时间格式
                    if started_at.endswith('Z'):
                        # 有Z后缀，移除并添加时区
                        started_at = started_at[:-1] + '+00:00'
                    elif '+' not in started_at and 'T' in started_at:
                        # 没有时区信息的ISO格式，假设为本地时间
                        pass  # 直接使用fromisoformat

                    if ended_at.endswith('Z'):
                        # 有Z后缀，移除并添加时区
                        ended_at = ended_at[:-1] + '+00:00'
                    elif '+' not in ended_at and 'T' in ended_at:
                        # 没有时区信息的ISO格式，假设为本地时间
                        pass  # 直接使用fromisoformat

                    start_time = datetime.fromisoformat(started_at)
                    end_time = datetime.fromisoformat(ended_at)
                    duration_seconds = (end_time - start_time).total_seconds()

                    # 只统计正数时长
                    if duration_seconds > 0:
                        total_duration += duration_seconds
                        sessions_with_duration += 1

                        # 分类到时长区间
                        if duration_seconds <= 30:
                            duration_distribution["0-30秒"] += 1
                        elif duration_seconds <= 120:  # 2分钟
                            duration_distribution["30秒-2分钟"] += 1
                        elif duration_seconds <= 300:  # 5分钟
                            duration_distribution["2-5分钟"] += 1
                        elif duration_seconds <= 600:  # 10分钟
                            duration_distribution["5-10分钟"] += 1
                        else:
                            duration_distribution["10分钟以上"] += 1

                except Exception as e:
                    # 如果时间解析失败，跳过这个会话
                    print(f"时间解析失败: {session.get('session_id', 'unknown')} - {e}")
                    continue
            else:
                # 如果没有ended_at，检查是否是进行中的会话
                if session.get('started_at') and session.get('status') == 'in_progress':
                    try:
                        started_at = session['started_at']
                        if started_at.endswith('Z'):
                            started_at = started_at[:-1] + '+00:00'

                        start_time = datetime.fromisoformat(started_at)
                        current_time = datetime.now(start_time.tzinfo)
                        duration_seconds = (current_time - start_time).total_seconds()

                        if duration_seconds > 0:
                            total_duration += duration_seconds
                            sessions_with_duration += 1

                            # 分类到时长区间
                            if duration_seconds <= 30:
                                duration_distribution["0-30秒"] += 1
                            elif duration_seconds <= 120:
                                duration_distribution["30秒-2分钟"] += 1
                            elif duration_seconds <= 300:
                                duration_distribution["2-5分钟"] += 1
                            elif duration_seconds <= 600:
                                duration_distribution["5-10分钟"] += 1
                            else:
                                duration_distribution["10分钟以上"] += 1

                    except Exception as e:
                        print(f"进行中会话时间解析失败: {session.get('session_id', 'unknown')} - {e}")
                        continue

        avg_duration = total_duration / sessions_with_duration if sessions_with_duration > 0 else 0

        # 调试信息
        print(f"统计调试: 总会话数={total_sessions}, 有时长数据的会话数={sessions_with_duration}")
        print(f"时长分布: {duration_distribution}")

        return JSONResponse({
            "success": True,
            "data": {
                "summary": {
                    "total_sessions": total_sessions,
                    "success_sessions": success_sessions,
                    "failed_sessions": failed_sessions,
                    "in_progress_sessions": in_progress_sessions,
                    "success_rate": (success_sessions / total_sessions * 100) if total_sessions > 0 else 0,
                    "total_rounds": total_rounds,
                    "avg_rounds": round(avg_rounds, 2),
                    "avg_duration": round(avg_duration, 2),
                    "sessions_with_duration": sessions_with_duration
                },
                "task_types": task_types,
                "daily_stats": daily_stats,
                "duration_distribution": duration_distribution,
                "date_range": {
                    "from": date_from,
                    "to": date_to,
                    "days": days
                }
            }
        })
    except Exception as e:
        return JSONResponse({
            "success": False,
            "error": str(e)
        }, status_code=500)


@app.get("/sessions", response_class=HTMLResponse)
async def sessions_page(request: Request):
    """会话列表页面"""
    return templates.TemplateResponse("sessions.html", {"request": request})


@app.get("/sessions/{session_id}", response_class=HTMLResponse)
async def session_detail_page(request: Request, session_id: str):
    """会话详情页面"""
    return templates.TemplateResponse("session_detail.html", {
        "request": request,
        "session_id": session_id
    })


@app.get("/statistics", response_class=HTMLResponse)
async def statistics_page(request: Request):
    """统计分析页面"""
    return templates.TemplateResponse("statistics.html", {"request": request})


@app.get("/analysis-demo", response_class=HTMLResponse)
async def analysis_demo_page(request: Request):
    """分析链路演示页面"""
    return templates.TemplateResponse("analysis_demo.html", {"request": request})


@app.get("/api/errors")
async def get_errors(
    hours: int = Query(24, description="收集最近几小时的错误"),
    level: Optional[str] = Query(None, description="错误级别过滤"),
    logger_name: Optional[str] = Query(None, description="日志器名称过滤"),
    exception_type: Optional[str] = Query(None, description="异常类型过滤"),
    query: Optional[str] = Query(None, description="搜索查询"),
    limit: int = Query(100, description="返回数量限制")
):
    """获取错误日志API"""
    try:
        # 收集所有错误
        all_errors = global_error_collector.collect_all_errors(hours=hours)

        # 搜索过滤
        filtered_errors = global_error_collector.search_errors(
            all_errors,
            query=query,
            level=level,
            logger_name=logger_name,
            exception_type=exception_type
        )

        # 限制返回数量
        limited_errors = filtered_errors[:limit]

        # 转换为字典格式
        error_dicts = []
        for error in limited_errors:
            error_dict = {
                "timestamp": error.timestamp,
                "level": error.level,
                "logger_name": error.logger_name,
                "message": error.message,
                "file_path": error.file_path,
                "line_number": error.line_number,
                "function_name": error.function_name,
                "exception_type": error.exception_type,
                "exception_message": error.exception_message,
                "traceback": error.traceback,
                "context": error.context,
                "source_file": error.source_file,
                "session_id": error.session_id,
                "task_id": error.task_id
            }
            error_dicts.append(error_dict)

        return JSONResponse({
            "success": True,
            "data": error_dicts,
            "total": len(filtered_errors),
            "collected_total": len(all_errors)
        })
    except Exception as e:
        return JSONResponse({
            "success": False,
            "error": str(e)
        }, status_code=500)


@app.get("/api/errors/statistics")
async def get_error_statistics(
    hours: int = Query(24, description="统计最近几小时的错误")
):
    """获取错误统计信息API"""
    try:
        # 收集错误
        all_errors = global_error_collector.collect_all_errors(hours=hours)

        # 获取统计信息
        stats = global_error_collector.get_error_statistics(all_errors)

        return JSONResponse({
            "success": True,
            "data": stats
        })
    except Exception as e:
        return JSONResponse({
            "success": False,
            "error": str(e)
        }, status_code=500)


@app.get("/errors", response_class=HTMLResponse)
async def errors_page(request: Request):
    """错误日志监控页面"""
    return templates.TemplateResponse("errors.html", {"request": request})


@app.post("/api/analyze-prompt")
async def analyze_prompt(request: Request):
    """分析提示词并提供优化建议"""
    try:
        data = await request.json()
        prompt = data.get('prompt', '')
        response = data.get('response', '')

        if not prompt:
            return JSONResponse({
                "success": False,
                "error": "提示词内容不能为空"
            }, status_code=400)

        # 分析提示词特征
        analysis = {
            "basic_stats": {
                "length": len(prompt),
                "lines": len(prompt.split('\n')),
                "estimated_tokens": len(prompt) // 4,
                "chinese_ratio": len([c for c in prompt if '\u4e00' <= c <= '\u9fa5']) / len(prompt) if prompt else 0
            },
            "structure_analysis": {
                "has_task_description": bool(re.search(r'任务|task|目标|goal', prompt, re.I)),
                "has_constraints": bool(re.search(r'约束|constraint|禁止|不要|必须', prompt, re.I)),
                "has_examples": bool(re.search(r'示例|example|例如|比如', prompt, re.I)),
                "has_steps": bool(re.search(r'步骤|step|第\d+步|首先|然后|最后', prompt, re.I)),
                "has_context": bool(re.search(r'上下文|context|背景|项目', prompt, re.I)),
                "has_tools": bool(re.search(r'工具|tool|coordinator|使用', prompt, re.I)),
                "has_format": bool(re.search(r'格式|format|输出|返回', prompt, re.I)),
                "code_blocks": len(re.findall(r'```', prompt)) // 2,
                "headings": len(re.findall(r'^#+\s', prompt, re.M))
            },
            "quality_issues": [],
            "suggestions": []
        }

        # 质量问题检测
        if not analysis["structure_analysis"]["has_task_description"]:
            analysis["quality_issues"].append("缺少明确的任务描述")
            analysis["suggestions"].append("添加清晰的任务目标和期望结果")

        if not analysis["structure_analysis"]["has_constraints"]:
            analysis["quality_issues"].append("缺少约束条件")
            analysis["suggestions"].append("添加明确的约束条件，如禁止事项、必须遵循的规则等")

        if not analysis["structure_analysis"]["has_steps"]:
            analysis["quality_issues"].append("缺少执行步骤")
            analysis["suggestions"].append("提供清晰的执行步骤，帮助AI理解任务流程")

        if analysis["basic_stats"]["length"] < 100:
            analysis["quality_issues"].append("提示词过短")
            analysis["suggestions"].append("增加更多上下文信息和详细说明")
        elif analysis["basic_stats"]["length"] > 4000:
            analysis["quality_issues"].append("提示词过长")
            analysis["suggestions"].append("精简内容，突出重点信息")

        if analysis["basic_stats"]["chinese_ratio"] < 0.3:
            analysis["quality_issues"].append("中文内容较少")
            analysis["suggestions"].append("增加中文说明，提高AI理解准确性")

        # 如果有响应，分析响应质量
        response_analysis = None
        if response:
            response_analysis = {
                "basic_stats": {
                    "length": len(response),
                    "lines": len(response.split('\n')),
                    "estimated_tokens": len(response) // 4,
                    "chinese_ratio": len([c for c in response if '\u4e00' <= c <= '\u9fa5']) / len(response)
                },
                "content_analysis": {
                    "has_code_execution": bool(re.search(r'```python|```javascript|```bash|tool_coordinator\.', response, re.I)),
                    "has_error_handling": bool(re.search(r'try|catch|except|error|失败|错误', response, re.I)),
                    "has_explanation": bool(re.search(r'因为|由于|原因|解释|说明', response, re.I)),
                    "has_results": bool(re.search(r'结果|result|成功|完成|输出', response, re.I)),
                    "is_relevant": not bool(re.search(r'算法|数据结构|排序|查找', response, re.I)),
                    "code_blocks": len(re.findall(r'```', response)) // 2
                },
                "quality_score": 0
            }

            # 计算响应质量分数
            if response_analysis["content_analysis"]["has_code_execution"]:
                response_analysis["quality_score"] += 25
            if response_analysis["content_analysis"]["has_error_handling"]:
                response_analysis["quality_score"] += 15
            if response_analysis["content_analysis"]["has_explanation"]:
                response_analysis["quality_score"] += 20
            if response_analysis["content_analysis"]["has_results"]:
                response_analysis["quality_score"] += 20
            if response_analysis["content_analysis"]["is_relevant"]:
                response_analysis["quality_score"] += 20

        # 计算总体质量分数
        quality_score = 0
        max_score = 100

        if analysis["structure_analysis"]["has_task_description"]:
            quality_score += 20
        if analysis["structure_analysis"]["has_constraints"]:
            quality_score += 15
        if analysis["structure_analysis"]["has_examples"]:
            quality_score += 15
        if analysis["structure_analysis"]["has_steps"]:
            quality_score += 20
        if analysis["structure_analysis"]["has_context"]:
            quality_score += 10
        if analysis["structure_analysis"]["has_tools"]:
            quality_score += 10
        if analysis["structure_analysis"]["has_format"]:
            quality_score += 10

        analysis["quality_score"] = quality_score
        analysis["quality_level"] = "优秀" if quality_score >= 80 else "良好" if quality_score >= 60 else "需要改进"

        return JSONResponse({
            "success": True,
            "data": {
                "prompt_analysis": analysis,
                "response_analysis": response_analysis,
                "overall_assessment": {
                    "prompt_quality": analysis["quality_level"],
                    "main_issues": analysis["quality_issues"][:3],  # 最多显示3个主要问题
                    "top_suggestions": analysis["suggestions"][:3]  # 最多显示3个建议
                }
            }
        })

    except Exception as e:
        return JSONResponse({
            "success": False,
            "error": str(e)
        }, status_code=500)


@app.get("/api/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}


if __name__ == "__main__":
    # 创建必要的目录
    os.makedirs("web_ui/templates", exist_ok=True)
    os.makedirs("web_ui/static/css", exist_ok=True)
    os.makedirs("web_ui/static/js", exist_ok=True)

    print("🌐 启动对话分析Web界面...")
    print("📊 访问地址: http://localhost:8080")
    print("🔍 功能:")
    print("  - 会话列表和搜索")
    print("  - 详细对话查看")
    print("  - 统计分析图表")
    print("  - 实时数据更新")

    uvicorn.run(
        "conversation_viewer_app:app",
        host="0.0.0.0",
        port=8080,
        reload=True,
        log_level="info"
    )
