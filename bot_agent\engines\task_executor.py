"""
任务执行引擎 - 基于Aider功能的任务执行引擎
复用Aider的Git操作、测试、Lint等功能，避免重复造轮子
"""

import logging
import os
import time
from typing import Dict, Any, List, Optional

from .tool_integration import AiderToolIntegration

from bot_agent.clients.gitlab_client import GitLabClient
from bot_agent.utils.gitlab_branch_manager import GitLabBranchManager
from bot_agent.config.model_config import ModelConfig
from bot_agent.memory.memory_integration import MemoryIntegration
from bot_agent.utils.llm_retry_handler import enhanced_llm_call
from bot_agent.utils.conversation_logger import global_conversation_logger, ConversationStatus
from bot_agent.utils.deadlock_monitor import MonitorContext, start_global_monitoring

logger = logging.getLogger(__name__)


class AiderBasedTaskExecutor:
    """
    基于Aider的任务执行引擎
    复用Aider的Coder、GitRepo、Commands等功能来执行任务
    """

    def __init__(self):
        """初始化任务执行引擎"""
        # 启动死循环监控
        start_global_monitoring()

        self.gitlab_client = GitLabClient()
        self.branch_manager = GitLabBranchManager(self.gitlab_client)

        # 获取项目下载目录
        self.projects_dir = os.environ.get("PROJECTS_DIR")
        if not self.projects_dir:
            parent_dir = os.path.dirname(os.getcwd())
            self.projects_dir = os.path.join(parent_dir, "git-repos")

        # 初始化工具集成
        self.tool_integration = AiderToolIntegration()

        # 初始化记忆集成
        self.memory_integration = MemoryIntegration()

        logger.info(f"AiderBasedTaskExecutor initialized with projects_dir: {self.projects_dir}")
        logger.info("🔍 死循环监控已启动")

    async def execute_task(self, task: Dict) -> Dict[str, Any]:
        """
        使用Aider执行任务

        Args:
            task: 任务对象

        Returns:
            Dict: 执行结果
        """
        task_id = task.get("id")
        logger.info(f"开始使用Aider执行任务 {task_id}")

        try:
            # 准备工作环境
            project_path = await self._prepare_workspace(task)

            # 使用Aider执行任务（集成记忆功能）
            result = await self._execute_with_aider(task, project_path)

            # 从任务完成中学习
            execution_result = {
                "status": "success",
                "task_id": task_id,
                "project_path": project_path,
                "report": result,
                "aider_based": True
            }

            # 异步学习，不阻塞返回
            try:
                await self.memory_integration.learn_from_task_completion(task, execution_result, project_path)
            except Exception as e:
                logger.warning(f"记忆学习失败: {e}")

            return execution_result

        except Exception as e:
            logger.error(f"执行任务 {task_id} 时出错: {e}", exc_info=True)
            return {
                "status": "error",
                "task_id": task_id,
                "error": str(e),
                "report": f"任务执行失败: {str(e)}"
            }

    async def _prepare_workspace(self, task: Dict) -> str:
        """
        准备工作环境

        Args:
            task: 任务对象

        Returns:
            str: 项目路径
        """
        with MonitorContext("prepare_workspace", max_duration=10.0) as monitor:
            metadata = task.get("metadata", {})
            project_id = metadata.get("project_id")

            monitor.update(f"获取项目ID: {project_id}")

            if not project_id:
                # 如果没有项目ID，使用当前目录
                monitor.update("无项目ID，使用当前目录")
                return os.getcwd()

            # 获取项目信息
            monitor.update(f"获取项目信息: {project_id}")
            project = self.gitlab_client.get_project(project_id)
            if not project:
                raise Exception(f"无法获取项目信息: {project_id}")

            project_name = project.get("name")
            project_path = os.path.join(self.projects_dir, project_name)
            monitor.update(f"项目路径: {project_path}")

            # 简化仓库设置，避免死循环
            try:
                monitor.update("检查本地仓库")
                # 检查本地仓库是否存在
                if os.path.exists(project_path) and os.path.exists(os.path.join(project_path, '.git')):
                    logger.info(f"使用现有仓库: {project_path}")
                    monitor.update("使用现有仓库")
                    return project_path
                else:
                    logger.warning(f"仓库不存在，使用当前目录: {os.getcwd()}")
                    monitor.update("仓库不存在，使用当前目录")
                    return os.getcwd()
            except Exception as e:
                logger.error(f"检查仓库时出错: {e}")
                monitor.update(f"检查仓库出错: {e}")
                return os.getcwd()

    async def _execute_with_aider(self, task: Dict, project_path: str) -> str:
        """
        使用Aider执行任务

        Args:
            task: 任务对象
            project_path: 项目路径

        Returns:
            str: 执行结果
        """
        try:
            # 导入Aider模块

            from aider.io import InputOutput
            from aider.models import Model
            from aider.coders import Coder
            from aider.repo import GitRepo

            # 构建任务描述
            title = task.get("title", "")
            description = task.get("description", "")
            task_type = task.get("analysis", {}).get("task_type", "UNKNOWN")

            # 检查任务是否过于模糊
            if self._is_vague_task(title, description):
                return await self._handle_vague_task(task, project_path)

            # 获取中文语言设置
            chat_language = os.getenv("AIDER_CHAT_LANGUAGE", "Chinese")

            # 准备记忆上下文
            memory_context = self.memory_integration.prepare_context_for_task(task, project_path)

            # 使用工具集成增强任务请求 - 明确指令，不要询问确认
            basic_request = f"""
任务类型: {task_type}
任务标题: {title}

任务描述:
{description}

{memory_context}

## 🚨 严格执行指令 - 禁止偏离

### ⚠️ 绝对禁止的行为：
1. **禁止创建任何新文件** - 不允许创建.py、.js、.md等任何新文件
2. **禁止编写代码** - 不允许编写类、函数、测试代码
3. **禁止基于假设分析** - 必须基于真实数据和日志
4. **禁止提供通用建议** - 必须针对具体问题提供具体解决方案
5. **禁止代码审查** - 当前任务不是代码审查，是问题分析

### ✅ 必须执行的步骤（严格按顺序）：

#### 第1步：获取真实数据（必须完成）
- 使用GitLabClient获取Job的实际日志内容
- 如果无法获取，明确说明原因并停止
- 不允许基于"没有日志"进行假设性分析

#### 第2步：分析具体问题（基于真实数据）
- 使用LogAnalysisTools分析实际的错误日志
- 识别具体的错误类型、文件、行号
- 确定失败的根本原因

#### 第3步：执行具体修复（针对性解决）
- 使用TerminalTools执行针对性的修复命令
- 修复具体识别出的问题
- 不执行通用的格式化命令

#### 第4步：验证修复效果
- 使用TestingTools验证修复是否成功
- 确认问题已解决

### 🎯 当前任务要求：
如果这是作业失败分析任务，你必须：
1. 获取指定Job ID的实际失败日志
2. 分析日志中的具体错误信息
3. 提供针对这些具体错误的修复方案
4. 验证修复效果

### 🚫 严格禁止：
- 说"Since we don't have the actual log files"
- 提供black、flake8等通用命令
- 创建LintAnalyzer等新类
- 进行代码审查
- 创建测试文件

现在开始执行，严格遵循上述要求：
"""

            # 使用工具集成增强请求
            full_request = await self.tool_integration.enhance_aider_request(
                basic_request,
                project_path,
                {"task": task, "task_type": task_type}
            )

            # 切换到项目目录
            original_cwd = os.getcwd()
            os.chdir(project_path)

            try:
                # 创建Aider组件 - 使用DeepSeek Chat进行代码生成
                model_name = ModelConfig.get_code_generation_model()
                logger.info(f"使用代码生成模型: {model_name}")
                model = Model(model_name)

                # 设置模型温度（如果支持）
                try:
                    if hasattr(model, 'temperature'):
                        model.temperature = 0.3
                    elif hasattr(model, 'use_temperature'):
                        model.use_temperature = 0.3
                except Exception as e:
                    logger.warning(f"无法设置模型温度: {e}")

                # 创建非交互式IO
                io = InputOutput(pretty=False, yes=True, chat_history_file=None)

                # 创建GitRepo
                repo = None
                try:
                    repo = GitRepo(io, [], project_path)
                except Exception as e:
                    logger.warning(f"无法创建GitRepo: {e}")

                # 智能分析项目结构，自动发现相关文件
                relevant_files = await self._smart_file_discovery(project_path, title, description)

                # 创建监控的Aider Coder - 完全透明的操作监控
                from bot_agent.aider_extensions.aider_monitor import aider_proxy

                logger.info("🔍 启用Aider操作监控系统")
                coder = aider_proxy.create_monitored_coder(
                    main_model=model,
                    io=io,
                    repo=repo,
                    fnames=relevant_files,  # 使用智能发现的文件
                    use_git=False,  # 🔧 禁用Git操作，避免卡住
                    auto_commits=False,  # 🔧 禁用自动提交
                    dirty_commits=False,  # 🔧 禁用脏提交
                    auto_lint=False,  # 🔧 禁用自动lint，避免安装工具
                    auto_test=False,  # 🔧 禁用自动测试，避免安装工具
                    stream=False,
                    verbose=False,
                    chat_language=chat_language,
                    suggest_shell_commands=False,  # 🔧 禁用shell命令建议
                    auto_accept_architect=True,  # 🚀 自动接受所有编辑建议
                    map_tokens=0,  # 🔧 禁用代码地图，减少处理时间
                    cache_prompts=False,  # 🔧 禁用提示缓存
                    num_cache_warming_pings=0  # 🔧 禁用缓存预热
                )

                # 智能多轮执行任务
                logger.info(f"使用优化的Aider执行任务: {title}")
                response = await self._intelligent_task_execution(coder, full_request, title, description, task)

                # 手动处理Git操作（如果需要）
                commit_status = "⚠️ 无Git仓库或无更改"
                push_status = "⚠️ 未推送"

                if repo:
                    try:
                        # 检查是否有更改
                        if repo.is_dirty():
                            commit_message = f"AI自动修改: {title}"
                            repo.commit(message=commit_message, aider_edits=True)
                            commit_status = "✅ 代码已提交到Git"
                            logger.info(f"代码已提交: {commit_message}")

                            # 推送到远程仓库
                            try:
                                import subprocess
                                import locale
                                system_encoding = locale.getpreferredencoding()

                                # 获取当前分支名
                                current_branch = subprocess.check_output(
                                    ["git", "rev-parse", "--abbrev-ref", "HEAD"],
                                    text=True,
                                    encoding=system_encoding,
                                    errors="replace",
                                    cwd=project_path
                                ).strip()

                                # 推送到远程
                                subprocess.run(
                                    ["git", "push", "origin", current_branch],
                                    check=True,
                                    encoding=system_encoding,
                                    errors="replace",
                                    cwd=project_path
                                )
                                push_status = f"✅ 代码已推送到远程分支 {current_branch}"
                                logger.info(f"代码已推送到远程分支: {current_branch}")

                            except subprocess.CalledProcessError as e:
                                push_status = f"❌ 推送失败: {e}"
                                logger.error(f"推送失败: {e}")
                            except Exception as e:
                                push_status = f"❌ 推送异常: {e}"
                                logger.error(f"推送异常: {e}")
                        else:
                            commit_status = "ℹ️ 无需提交（无更改）"
                            push_status = "ℹ️ 无需推送"

                    except Exception as e:
                        commit_status = f"❌ 提交失败: {e}"
                        logger.error(f"提交失败: {e}")

                # 获取Aider操作监控报告
                monitoring_report = aider_proxy.get_operation_summary()

                return f"""
## 🎯 任务执行完成

**任务标题**: {title}
**任务类型**: {task_type}
**项目路径**: {project_path}

### 🤖 Aider AI执行结果:
{response or "任务已完成，请检查代码修改"}

### 📊 执行状态:
- ✅ 代码修改完成
- ✅ 使用监控的Aider AI处理
- {commit_status}
- {push_status}

{monitoring_report}

### 📝 下一步:
请检查修改的代码是否符合要求。如果满意，可以创建Merge Request进行代码审查。
"""

            finally:
                # 恢复原始工作目录
                os.chdir(original_cwd)

        except Exception as e:
            logger.error(f"使用Aider执行任务失败: {e}", exc_info=True)
            raise Exception(f"Aider执行失败: {str(e)}")

    async def _smart_file_discovery(self, project_path: str, title: str, description: str) -> List[str]:
        """
        智能文件发现 - 基于任务内容自动发现相关文件

        Args:
            project_path: 项目路径
            title: 任务标题
            description: 任务描述

        Returns:
            List[str]: 相关文件列表
        """
        try:
            logger.info("🔍 开始智能文件发现...")

            # 1. 从任务描述中提取文件路径
            mentioned_files = self._extract_file_paths_from_text(title + "\n" + description)

            # 2. 基于关键词智能推断相关文件
            keyword_files = await self._discover_files_by_keywords(project_path, title, description)

            # 3. 分析项目结构，找到核心文件
            core_files = self._discover_core_files(project_path)

            # 4. 合并并去重
            all_files = list(set(mentioned_files + keyword_files + core_files))

            # 5. 过滤存在的文件
            existing_files = []
            for file_path in all_files:
                full_path = os.path.join(project_path, file_path)
                if os.path.exists(full_path):
                    existing_files.append(file_path)

            logger.info(f"智能发现了 {len(existing_files)} 个相关文件: {existing_files}")
            return existing_files

        except Exception as e:
            logger.warning(f"智能文件发现失败: {e}")
            return []

    def _extract_file_paths_from_text(self, text: str) -> List[str]:
        """从文本中提取文件路径"""
        import re

        file_patterns = [
            r'`([^`]+\.(py|js|json|yml|yaml|md|txt|html|css|sql))`',  # 反引号包围的文件
            r'"([^"]+\.(py|js|json|yml|yaml|md|txt|html|css|sql))"',  # 双引号包围的文件
            r"'([^']+\.(py|js|json|yml|yaml|md|txt|html|css|sql))'",  # 单引号包围的文件
            r'(\w+/[\w/]+\.(py|js|json|yml|yaml|md|txt|html|css|sql))',  # 路径格式的文件
        ]

        files = []
        for pattern in file_patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                if isinstance(match, tuple):
                    files.append(match[0])
                else:
                    files.append(match)

        return files

    async def _discover_files_by_keywords(self, project_path: str, title: str, description: str) -> List[str]:
        """基于关键词发现相关文件"""
        content = (title + "\n" + description).lower()
        discovered_files = []

        # 关键词到文件的映射
        keyword_mappings = {
            # API相关
            "api": ["api", "routes", "endpoints", "views"],
            "接口": ["api", "routes", "endpoints", "views"],
            "路由": ["routes", "router", "urls"],

            # 数据库相关
            "数据库": ["models", "database", "db", "schema"],
            "model": ["models", "database", "db"],
            "sql": ["models", "database", "migrations"],

            # 配置相关
            "配置": ["config", "settings", "env"],
            "config": ["config", "settings", "env"],
            "环境": ["config", "env", "settings"],

            # 认证相关
            "认证": ["auth", "login", "user", "security"],
            "登录": ["auth", "login", "user"],
            "用户": ["user", "auth", "account"],
            "密钥": ["auth", "security", "keys", "token"],
            "token": ["auth", "security", "token"],

            # 测试相关
            "测试": ["test", "tests"],
            "test": ["test", "tests"],

            # 工具相关
            "工具": ["utils", "helpers", "tools"],
            "utils": ["utils", "helpers", "tools"],
            "helper": ["utils", "helpers", "tools"],
        }

        # 根据关键词查找文件
        for keyword, file_patterns in keyword_mappings.items():
            if keyword in content:
                for pattern in file_patterns:
                    # 查找匹配的文件
                    for root, _, files in os.walk(project_path):
                        for file in files:
                            if (pattern in file.lower() or pattern in root.lower()) and file.endswith('.py'):
                                rel_path = os.path.relpath(os.path.join(root, file), project_path)
                                discovered_files.append(rel_path)

        return list(set(discovered_files))

    def _discover_core_files(self, project_path: str) -> List[str]:
        """发现项目核心文件"""
        core_files = []

        # 常见的核心文件名
        core_file_names = [
            "main.py", "app.py", "run.py", "server.py",
            "__init__.py", "settings.py", "config.py",
            "models.py", "views.py", "routes.py",
            "requirements.txt", "setup.py"
        ]

        # 查找核心文件
        for root, dirs, files in os.walk(project_path):
            # 跳过隐藏目录和常见忽略目录
            dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['__pycache__', 'node_modules', '.git']]

            for file in files:
                if file in core_file_names:
                    rel_path = os.path.relpath(os.path.join(root, file), project_path)
                    core_files.append(rel_path)

        return core_files

    def _is_vague_task(self, title: str, description: str) -> bool:
        """
        检查任务是否过于模糊，无法进行具体的代码操作

        Args:
            title: 任务标题
            description: 任务描述

        Returns:
            bool: 是否为模糊任务
        """
        # 合并标题和描述
        content = f"{title}\n{description}".strip().lower()

        # 检查内容长度
        if len(content) < 10:
            return True

        # 模糊任务的特征词
        vague_patterns = [
            # 中文模糊词
            "继续", "继续啊", "继续吧", "接着", "接着来", "下一步", "然后", "然后呢",
            "怎么办", "怎么做", "做什么", "干什么", "搞什么", "弄什么",
            "帮忙", "帮助", "协助", "支持", "处理", "解决",
            "看看", "检查", "分析", "研究", "了解", "学习",
            "好的", "可以", "行", "嗯", "哦", "额", "呃",

            # 英文模糊词
            "continue", "next", "help", "assist", "check", "analyze",
            "what", "how", "why", "ok", "yes", "sure", "fine",
            "do something", "help me", "what next", "continue work",
        ]

        # 检查是否只包含模糊词
        for pattern in vague_patterns:
            if pattern in content and len(content.replace(pattern, "").strip()) < 5:
                return True

        # 检查是否缺乏具体的技术词汇
        tech_keywords = [
            # 编程相关
            "代码", "函数", "类", "方法", "变量", "接口", "api", "数据库", "文件",
            "code", "function", "class", "method", "variable", "interface", "database", "file",

            # 操作相关
            "创建", "修改", "删除", "添加", "实现", "优化", "修复", "测试", "部署",
            "create", "modify", "delete", "add", "implement", "optimize", "fix", "test", "deploy",

            # 技术栈
            "python", "javascript", "java", "react", "vue", "django", "flask", "fastapi",
            "mysql", "postgresql", "redis", "docker", "kubernetes", "git", "github", "gitlab"
        ]

        has_tech_content = any(keyword in content for keyword in tech_keywords)

        # 如果没有技术内容且内容很短，认为是模糊任务
        if not has_tech_content and len(content) < 50:
            return True

        return False

    async def _handle_vague_task(self, task: Dict, project_path: str) -> str:
        """
        处理模糊任务 - 通过项目分析提供具体建议

        Args:
            task: 任务对象
            project_path: 项目路径

        Returns:
            str: 处理结果
        """
        try:
            logger.info("🤔 检测到模糊任务，开始智能分析...")

            title = task.get("title", "")
            description = task.get("description", "")

            # 分析项目结构
            project_analysis = await self._analyze_project_structure(project_path)

            # 生成具体建议
            suggestions = await self._generate_task_suggestions(project_analysis, title, description)

            return f"""
## 🤔 任务需要更多信息

**检测到的问题**: 任务描述过于模糊，无法进行具体的代码操作。

**任务标题**: {title}
**任务描述**: {description or "（无具体描述）"}

### 📊 项目分析结果:
{project_analysis}

### 💡 建议的具体任务:
{suggestions}

### 📝 如何提供更好的任务描述:

1. **明确目标**: 说明你想要实现什么功能
2. **指定文件**: 提及需要修改的具体文件
3. **描述需求**: 详细说明功能要求和预期效果
4. **提供示例**: 如果可能，提供代码示例或参考

**示例好的任务描述**:
- "在 `api/routes.py` 中添加用户登录接口"
- "修复 `models/user.py` 中的密码验证逻辑"
- "为 `utils/database.py` 添加连接池功能"
- "优化 `services/cache.py` 的性能问题"

请提供更具体的任务描述，我将为您进行精确的代码修改。
"""

        except Exception as e:
            logger.error(f"处理模糊任务失败: {e}")
            return f"""
## ❌ 任务处理失败

**错误**: 无法处理当前任务 - {str(e)}

**任务标题**: {title}
**任务描述**: {description or "（无描述）"}

请提供更具体和详细的任务描述，包括：
1. 明确的功能需求
2. 需要修改的文件
3. 预期的实现效果

这样我就能为您提供精确的代码修改服务。
"""

    async def _analyze_project_structure(self, project_path: str) -> str:
        """
        分析项目结构，了解项目的主要组成部分

        Args:
            project_path: 项目路径

        Returns:
            str: 项目结构分析结果
        """
        try:
            # 获取项目主要文件和目录
            main_files = []
            main_dirs = []

            for item in os.listdir(project_path):
                item_path = os.path.join(project_path, item)
                if os.path.isfile(item_path) and item.endswith(('.py', '.js', '.json', '.md', '.yml', '.yaml')):
                    main_files.append(item)
                elif os.path.isdir(item_path) and not item.startswith('.') and item != '__pycache__':
                    main_dirs.append(item)

            # 分析项目类型
            project_type = "未知项目"
            if "requirements.txt" in main_files or "pyproject.toml" in main_files:
                project_type = "Python项目"
            elif "package.json" in main_files:
                project_type = "Node.js项目"
            elif "pom.xml" in main_files:
                project_type = "Java项目"
            elif "Cargo.toml" in main_files:
                project_type = "Rust项目"

            return f"""
**项目类型**: {project_type}

**主要目录**: {', '.join(main_dirs[:10]) if main_dirs else '无'}

**主要文件**: {', '.join(main_files[:10]) if main_files else '无'}

**项目规模**: 约 {len(main_files)} 个主要文件，{len(main_dirs)} 个目录
"""

        except Exception as e:
            logger.error(f"分析项目结构失败: {e}")
            return f"项目结构分析失败: {str(e)}"

    async def _generate_task_suggestions(self, project_analysis: str, title: str, description: str) -> str:
        """
        基于项目分析生成任务建议

        Args:
            project_analysis: 项目分析结果
            title: 任务标题
            description: 任务描述

        Returns:
            str: 任务建议
        """
        # 基于项目类型和常见需求生成建议
        suggestions = [
            "🔧 **代码优化**: 优化现有代码的性能和可读性",
            "🧪 **添加测试**: 为核心功能添加单元测试",
            "📚 **完善文档**: 添加或更新项目文档和代码注释",
            "🔒 **安全加固**: 检查和修复潜在的安全问题",
            "🐛 **Bug修复**: 修复已知的错误和问题",
            "✨ **新功能**: 添加新的功能模块",
            "🔄 **重构代码**: 重构复杂的代码结构",
            "⚡ **性能优化**: 提升系统性能和响应速度",
        ]

        return "\n".join(suggestions)

    async def _intelligent_task_execution(self, coder, initial_request: str, title: str, description: str, task: Dict = None) -> str:
        """
        智能任务执行 - 多轮对话，自动修复错误，持续改进

        Args:
            coder: Aider Coder实例
            initial_request: 初始请求
            title: 任务标题
            description: 任务描述
            task: 任务对象（包含metadata等信息）

        Returns:
            str: 执行结果
        """
        # 获取正确的项目路径
        try:
            if task:
                project_path = await self._prepare_workspace(task)
            else:
                project_path = os.getcwd()
        except Exception as e:
            logger.warning(f"无法获取项目路径，使用当前目录: {e}")
            project_path = os.getcwd()

        # 开始对话记录会话
        session_id = global_conversation_logger.start_session(
            task_id=f"task_{int(time.time())}",
            task_title=title,
            task_type="intelligent_execution",
            project_path=project_path,
            metadata={
                "description": description,
                "initial_request": initial_request,
                "task_metadata": task.get("metadata", {}) if task else {}
            }
        )

        try:
            logger.info("🤖 开始智能任务执行...")

            # 检查是否为作业失败分析任务
            if self._is_job_failure_analysis_task(title, description):
                logger.info("🔍 检测到作业失败分析任务，使用专门的分析器")
                return await self._handle_job_failure_analysis(session_id, title, description, task=task)

            # 第1轮：初始实现 - 使用重试机制
            logger.info("📝 第1轮：初始实现")
            round_start_time = time.time()

            async def run_initial_implementation():
                result = coder.run(with_message=initial_request)
                if result and len(result.strip()) > 10:
                    return result
                else:
                    return None  # 触发重试

            task_info = {
                "title": f"初始实现: {title}",
                "task_type": "initial_implementation"
            }

            response1 = await enhanced_llm_call(
                run_initial_implementation,
                task_info=task_info
            )

            # 记录第1轮对话
            round_duration = time.time() - round_start_time
            global_conversation_logger.log_round(
                session_id=session_id,
                round_number=1,
                round_name="初始实现",
                prompt=initial_request,
                response=response1,
                model_name="aider-integrated",
                duration=round_duration,
                status=ConversationStatus.SUCCESS if response1 else ConversationStatus.FAILED
            )

            # 第2轮：检查是否实际创建了代码，如果没有则强制执行
            logger.info("🔍 第2轮：检查并强制执行代码创建")
            round2_start_time = time.time()

            # 检查第一轮是否实际创建了代码
            if "请确认" in response1 or "是否允许" in response1 or "希望先讨论" in response1:
                # 第一轮只是询问确认，强制执行实际代码创建
                force_request = f"""
上一轮你只是询问了确认，现在请直接执行代码创建，不要再询问任何确认。

强制执行指令：
1. 立即创建所有必需的文件和代码
2. 不要询问任何确认或讨论
3. 直接实现完整的功能
4. 创建目录结构和所有代码文件
5. 使用中文注释

任务要求：{description}

现在立即开始创建代码文件，不要再询问确认！
"""
                # 使用重试机制执行强制请求
                async def run_force_implementation():
                    result = coder.run(with_message=force_request)
                    if result and len(result.strip()) > 10:
                        return result
                    else:
                        return None

                task_info_force = {
                    "title": f"强制实现: {title}",
                    "task_type": "force_implementation"
                }

                response2 = await enhanced_llm_call(
                    run_force_implementation,
                    task_info=task_info_force
                )

                # 记录第2轮对话（强制实现）
                round2_duration = time.time() - round2_start_time
                global_conversation_logger.log_round(
                    session_id=session_id,
                    round_number=2,
                    round_name="强制实现",
                    prompt=force_request,
                    response=response2,
                    model_name="aider-integrated",
                    duration=round2_duration,
                    status=ConversationStatus.SUCCESS if response2 else ConversationStatus.FAILED
                )
            else:
                # 第一轮已经创建了代码，进行审查和改进
                review_request = f"""
请审查刚才实现的代码，检查以下方面：

1. 代码质量和最佳实践
2. 错误处理和边界情况
3. 性能优化机会
4. 安全性考虑
5. 代码注释和文档

如果发现问题，请直接修复。如果代码已经很好，请说明"代码质量良好，无需修改"。

请用中文详细说明你的检查结果和修改内容。
"""

                # 使用重试机制执行审查
                async def run_code_review():
                    result = coder.run(with_message=review_request)
                    if result and len(result.strip()) > 10:
                        return result
                    else:
                        return None

                task_info_review = {
                    "title": f"代码审查: {title}",
                    "task_type": "code_review"
                }

                response2 = await enhanced_llm_call(
                    run_code_review,
                    task_info=task_info_review
                )

                # 记录第2轮对话（代码审查）
                round2_duration = time.time() - round2_start_time
                global_conversation_logger.log_round(
                    session_id=session_id,
                    round_number=2,
                    round_name="代码审查",
                    prompt=review_request,
                    response=response2,
                    model_name="aider-integrated",
                    duration=round2_duration,
                    status=ConversationStatus.SUCCESS if response2 else ConversationStatus.FAILED
                )

            # 第3轮：检查是否需要强制创建更多代码
            logger.info("🔧 第3轮：确保代码完整性")

            # 检查前两轮是否真正创建了实质性代码
            combined_response = response1 + "\n" + response2
            if ("请确认" in combined_response or "是否允许" in combined_response or
                "希望先讨论" in combined_response or len(combined_response.strip()) < 200):
                # 前两轮没有创建足够的代码，第三轮强制创建
                final_force_request = f"""
前面的回复仍然不够具体，现在必须创建实际的代码文件。

最终强制指令：
1. 必须创建完整的Python代码文件
2. 必须包含所有必要的类和函数
3. 必须创建项目目录结构
4. 代码必须可以直接运行
5. 不允许再询问任何确认

任务：{description}

现在立即创建所有代码文件，包括：
- 主要的Python模块文件
- 配置文件
- 示例使用代码
- README文档

开始执行，创建实际的代码文件！
"""
                response3 = coder.run(with_message=final_force_request)
            elif "测试" in description or "test" in description.lower():
                # 如果任务要求测试，添加测试
                test_request = f"""
为刚才实现的功能添加完整的测试：

1. 单元测试 - 测试核心功能
2. 边界测试 - 测试边界情况
3. 错误测试 - 测试错误处理
4. 集成测试 - 测试整体流程

请直接创建测试文件，包含详细的中文注释。
"""
                response3 = coder.run(with_message=test_request)
            else:
                response3 = "代码创建完成，无需额外测试"

            # 第4轮：最终验证和文档
            logger.info("✅ 第4轮：最终验证和文档")
            final_request = f"""
请进行最终验证和完善：

1. 确认所有代码文件都已创建并保存
2. 检查代码语法和逻辑正确性
3. 添加使用说明和示例
4. 创建或更新README文档
5. 确保项目结构清晰

请总结已创建的文件和实现的功能，用中文详细说明。
"""
            response4 = coder.run(with_message=final_request)

            # 合并所有响应
            full_response = f"""
## 🎯 智能Aider执行报告

### 📝 第1轮 - 初始实现
{response1 or "初始实现完成"}

### 🔍 第2轮 - 代码审查和改进
{response2 or "代码审查完成"}

### 🧪 第3轮 - 测试添加
{response3 or "测试处理完成"}

### ✅ 第4轮 - 最终验证
{response4 or "最终验证完成"}

---
*智能Aider多轮执行完成 - 自动审查、改进、测试、验证*
"""

            logger.info("🎉 智能任务执行完成")

            # 结束对话会话
            global_conversation_logger.end_session(
                session_id=session_id,
                final_result=full_response,
                status=ConversationStatus.SUCCESS
            )

            return full_response

        except Exception as e:
            logger.error(f"智能任务执行失败: {e}")

            # 记录失败的会话
            global_conversation_logger.end_session(
                session_id=session_id,
                final_result=None,
                status=ConversationStatus.FAILED,
                error_summary=str(e)
            )

            # 如果智能执行失败，回退到简单执行
            logger.info("回退到简单执行模式...")
            return coder.run(with_message=initial_request)

    def _is_job_failure_analysis_task(self, title: str, description: str) -> bool:
        """检测是否为作业失败分析任务"""
        job_failure_keywords = [
            "作业失败分析",
            "job failure",
            "ci/cd失败",
            "pipeline失败",
            "构建失败",
            "测试失败",
            "部署失败",
            "lint失败",
            "智能分析job",  # 添加新的关键词
            "分析job",      # 添加新的关键词
            "失败原因",     # 添加新的关键词
            "执行修复"      # 添加新的关键词
        ]

        text = f"{title} {description}".lower()
        return any(keyword.lower() in text for keyword in job_failure_keywords)

    async def _handle_job_failure_analysis(self, session_id: str, title: str, description: str, task: Dict = None) -> str:
        """处理作业失败分析任务"""
        with MonitorContext("job_failure_analysis", max_duration=300.0) as monitor:
            try:
                monitor.update("开始作业失败分析")

                # 从标题或描述中提取Job ID
                monitor.update("提取Job ID")
                job_id = self._extract_job_id(title, description)
                if not job_id:
                    error_msg = "无法从任务描述中提取Job ID"
                    monitor.update(f"Job ID提取失败: {error_msg}")
                    global_conversation_logger.log_round(
                        session_id=session_id,
                        round_number=1,
                        round_name="Job ID提取失败",
                        prompt=f"标题: {title}\n描述: {description}",
                        response=error_msg,
                        model_name="job-failure-analyzer",
                        duration=0.1,
                        status=ConversationStatus.FAILED,
                        error_message=error_msg
                    )
                    return error_msg

                logger.info(f"提取到Job ID: {job_id}")
                monitor.update(f"成功提取Job ID: {job_id}")

                # 从任务metadata中获取项目ID
                monitor.update("获取项目ID")
                project_id = None
                if task and task.get("metadata"):
                    project_id = task["metadata"].get("project_id")
                    logger.info(f"从任务metadata中获取到项目ID: {project_id}")
                    monitor.update(f"获取到项目ID: {project_id}")

                # 获取正确的项目路径并更新会话信息
                monitor.update("准备工作空间")
                try:
                    # 直接使用已经准备好的项目路径，避免重复调用
                    if task and task.get("metadata"):
                        project_name = task["metadata"].get("project_name", "unknown")
                        project_path = os.path.join(self.projects_dir, project_name)
                        if not os.path.exists(project_path):
                            project_path = os.getcwd()
                    else:
                        project_path = os.getcwd()

                    logger.info(f"作业失败分析使用项目路径: {project_path}")
                    monitor.update(f"项目路径: {project_path}")

                    # 更新会话的项目路径
                    if session_id in global_conversation_logger.active_sessions:
                        global_conversation_logger.active_sessions[session_id].project_path = project_path
                        logger.info(f"已更新会话 {session_id} 的项目路径为: {project_path}")
                        monitor.update("会话路径已更新")

                except Exception as e:
                    logger.warning(f"无法获取项目路径，使用当前目录: {e}")
                    project_path = os.getcwd()
                    monitor.update(f"使用当前目录: {project_path}")

                # 使用智能化作业失败分析系统
                monitor.update("开始智能分析")
                round_start_time = time.time()

                # 直接使用工具协调器方案：预执行数据收集 + 自动修复
                monitor.update("预执行数据收集")

                from bot_agent.tools.intelligent_tool_coordinator import global_tool_coordinator

                # 第1步：预先收集所有数据
                job_data_result = global_tool_coordinator.get_job_info_and_log(job_id, project_id)

                if not job_data_result.success:
                    analysis_result = f"❌ 无法获取作业数据: {job_data_result.message}"
                else:
                    job_info = job_data_result.data['job_info']
                    job_log = job_data_result.data['job_log']

                    # 第2步：预先分析错误
                    error_analysis_result = global_tool_coordinator.analyze_job_errors(job_log, job_info)

                    # 构建包含真实数据的AI分析请求
                    log_preview = '\\n'.join(job_log.split('\\n')[-20:]) if job_log else "无日志内容"
                    error_summary = ""
                    if error_analysis_result.success:
                        errors = error_analysis_result.data.get('all_errors', [])
                        error_summary = '\\n'.join(f"- {error}" for error in errors[:10])

                    intelligent_analysis_request = f"""
## 🤖 GitLab CI/CD作业失败智能分析

你是一个专业的DevOps专家。基于以下真实数据，请分析作业失败原因并提供修复方案。

### 📋 作业信息
- **作业ID**: {job_id}
- **作业名称**: {job_info.get('name', 'unknown')}
- **作业状态**: {job_info.get('status', 'unknown')}
- **项目路径**: {project_path}
- **日志长度**: {len(job_log)} 字符

### 🔍 关键日志片段（最后20行）
```
{log_preview}
```

### ⚠️ 检测到的错误
{error_summary if error_summary else "未检测到明显错误"}

### 🎯 请提供以下分析

1. **错误根因分析**：
   - 分析日志中的具体错误
   - 确定失败的根本原因
   - 识别相关的文件和代码行

2. **修复方案**：
   - 提供具体的修复命令
   - 说明修复步骤
   - 预期的修复效果

3. **预防措施**：
   - 如何避免类似问题
   - 改进建议

### 📝 输出格式
请用中文回复，格式如下：

## 🔍 错误分析
[详细分析错误原因]

## 🔧 修复方案
```bash
# 具体的修复命令
command1
command2
```

## 💡 预防措施
[预防建议]

现在开始分析！
"""

                # 使用多轮交互修复，而不是硬编码修复过程
                monitor.update("启动多轮交互智能修复")

                try:
                    # 第3步：使用多轮交互修复系统
                    if error_analysis_result.success:
                        monitor.update("执行多轮交互修复")

                        # 使用AI生成动态修复方案，而不是预定义策略
                        fix_result = await self._execute_multi_round_intelligent_fix(
                            error_analysis_result.data,
                            project_path,
                            job_info
                        )

                        # 第4步：验证修复效果
                        verify_result = global_tool_coordinator.verify_fixes(
                            project_path,
                            job_info.get('name', 'lint')
                        )

                        # 构建分析报告
                        analysis_result = f"""
## 🤖 GitLab CI/CD作业失败智能分析与修复报告

### 📋 作业信息
- **作业ID**: {job_id}
- **作业名称**: {job_info.get('name', 'unknown')}
- **作业状态**: {job_info.get('status', 'unknown')}
- **项目路径**: {project_path}
- **日志长度**: {len(job_log)} 字符

### 🔍 关键日志片段（最后20行）
```
{log_preview}
```

### ⚠️ 检测到的错误
{error_summary if error_summary else "未检测到明显错误"}

### 🔧 自动修复执行
- **修复状态**: {'✅ 成功' if fix_result.success else '❌ 失败'}
- **修复详情**: {fix_result.data if fix_result.success else fix_result.message}

### ✅ 修复验证
- **验证状态**: {'✅ 成功' if verify_result.success else '❌ 失败'}
- **验证详情**: {verify_result.data if verify_result.success else verify_result.message}

### 📝 总结
作业失败分析和修复流程已完成。系统自动检测错误并执行修复操作。
"""
                    else:
                        analysis_result = f"""
## 🤖 GitLab CI/CD作业失败分析报告

### 📋 作业信息
- **作业ID**: {job_id}
- **项目路径**: {project_path}

### ❌ 分析结果
无法分析作业错误: {error_analysis_result.message}

### 📝 建议
请检查作业日志和配置，手动分析失败原因。
"""

                except Exception as e:
                    analysis_result = f"智能修复执行出错: {str(e)}"
                    logger.error(f"智能修复执行出错: {e}")

                # 分析完成
                monitor.update("AI智能分析完成")

                round_duration = time.time() - round_start_time
                status = ConversationStatus.SUCCESS
                monitor.update("智能分析完成")

                # 记录智能分析过程 - 使用真正发送给AI的提示词
                global_conversation_logger.log_round(
                    session_id=session_id,
                    round_number=1,
                    round_name="智能作业失败分析",
                    prompt=intelligent_analysis_request,  # 记录真正的提示词
                    response=analysis_result,
                    model_name="intelligent-job-analyzer",
                    duration=round_duration,
                    status=status
                )

                # 结束会话
                global_conversation_logger.end_session(
                    session_id=session_id,
                    final_result=analysis_result,
                    status=status
                )

                return analysis_result

            except Exception as e:
                error_msg = f"作业失败分析过程出错: {str(e)}"
                logger.error(error_msg)
                monitor.update(f"分析失败: {error_msg}")

                global_conversation_logger.log_round(
                    session_id=session_id,
                    round_number=1,
                    round_name="分析过程错误",
                    prompt=f"分析任务: {title}",
                    response=error_msg,
                    model_name="job-failure-analyzer",
                    duration=0.1,
                    status=ConversationStatus.FAILED,
                    error_message=str(e)
                )

                global_conversation_logger.end_session(
                    session_id=session_id,
                    final_result=None,
                    status=ConversationStatus.FAILED,
                    error_summary=str(e)
                )

                return error_msg

    def _extract_job_id(self, title: str, description: str) -> Optional[int]:
        """从标题或描述中提取Job ID"""
        import re

        # 尝试从标题中提取
        title_match = re.search(r'Job (\d+)', title)
        if title_match:
            return int(title_match.group(1))

        # 尝试从标题和描述中提取
        combined_text = f"{title} {description}"
        desc_patterns = [
            r'作业ID[：:]\s*(\d+)',
            r'Job ID[：:]\s*(\d+)',
            r'job_id[：:]\s*(\d+)',
            r'Job (\d+)',
            r'job (\d+)',
            r'作业ID:\s*(\d+)',  # 添加中文冒号支持
            r'作业ID：\s*(\d+)'   # 添加中文全角冒号支持
        ]

        for pattern in desc_patterns:
            match = re.search(pattern, combined_text, re.IGNORECASE)
            if match:
                return int(match.group(1))

        return None



    async def _auto_error_recovery(self, coder, error_message: str) -> str:
        """
        自动错误恢复 - 当出现错误时自动尝试修复

        Args:
            coder: Aider Coder实例
            error_message: 错误信息

        Returns:
            str: 修复结果
        """
        try:
            logger.info(f"🔧 开始自动错误恢复: {error_message}")

            recovery_request = f"""
检测到以下错误，请自动修复：

错误信息: {error_message}

请分析错误原因并自动修复：
1. 检查语法错误
2. 检查导入问题
3. 检查文件路径
4. 检查依赖关系
5. 修复逻辑错误

请直接修复代码，不要询问，用中文说明修复过程。
"""

            recovery_response = coder.run(with_message=recovery_request)
            logger.info("✅ 自动错误恢复完成")
            return recovery_response

        except Exception as e:
            logger.error(f"自动错误恢复失败: {e}")
            return f"自动错误恢复失败: {str(e)}"

    async def _execute_multi_round_intelligent_fix(self, error_analysis: Dict[str, Any], project_path: str, job_info: Dict[str, Any]) -> Any:
        """
        执行多轮交互智能修复 - 使用AI动态生成修复方案，而不是硬编码策略

        Args:
            error_analysis: 错误分析结果
            project_path: 项目路径
            job_info: 作业信息

        Returns:
            修复结果
        """
        try:
            logger.info("🚀 启动多轮交互智能修复系统...")

            from bot_agent.tools.intelligent_tool_coordinator import global_tool_coordinator

            # 获取所有错误
            all_errors = error_analysis.get('all_errors', [])
            if not all_errors:
                return type('ToolResult', (), {
                    'success': False,
                    'message': '没有发现需要修复的错误',
                    'data': {}
                })()

            logger.info(f"🔍 发现 {len(all_errors)} 个错误，开始智能修复...")

            # 使用AI生成动态修复方案
            ai_fix_result = await global_tool_coordinator._ai_generate_fix_plan(all_errors, project_path)

            if not ai_fix_result.success:
                logger.warning("AI修复方案生成失败，使用多轮交互策略修复")
                return await self._multi_round_strategy_fix(all_errors, project_path, job_info)

            # 执行AI生成的修复方案（使用多轮交互）
            fix_plan = ai_fix_result.data.get('fix_plan', [])
            logger.info(f"🤖 AI生成了 {len(fix_plan)} 个修复步骤")

            fix_results = []
            successful_fixes = 0

            for i, fix_step in enumerate(fix_plan):
                logger.info(f"🔧 执行修复步骤 {i+1}/{len(fix_plan)}: {fix_step.get('description', '')}")

                # 使用多轮重试执行每个修复步骤
                step_result = await global_tool_coordinator._execute_ai_fix_step(fix_step, project_path)
                fix_results.append(step_result)

                if step_result.get('success', False):
                    successful_fixes += 1
                    logger.info(f"✅ 步骤 {i+1} 执行成功")
                else:
                    logger.warning(f"❌ 步骤 {i+1} 执行失败: {step_result.get('message', '')}")

                    # 如果关键步骤失败，尝试AI生成替代方案
                    if fix_step.get('critical', False):
                        logger.info(f"🔄 关键步骤失败，尝试生成替代方案...")
                        alternative_result = await self._generate_alternative_fix(fix_step, step_result, project_path)
                        if alternative_result.get('success', False):
                            fix_results.append(alternative_result)
                            successful_fixes += 1
                            logger.info(f"✅ 替代方案执行成功")

            # 构建修复结果
            return type('ToolResult', (), {
                'success': successful_fixes > 0,
                'message': f"多轮智能修复执行了 {len(fix_plan)} 个步骤，成功 {successful_fixes} 个",
                'data': {
                    'fix_results': fix_results,
                    'total_fixes': len(fix_plan),
                    'successful_fixes': successful_fixes,
                    'success_rate': successful_fixes / len(fix_plan) if len(fix_plan) > 0 else 0,
                    'ai_generated': True,
                    'multi_round': True,
                    'fix_plan': fix_plan
                }
            })()

        except Exception as e:
            logger.error(f"多轮交互智能修复失败: {e}")
            return type('ToolResult', (), {
                'success': False,
                'message': f"多轮智能修复失败: {str(e)}",
                'data': {}
            })()

    async def _multi_round_strategy_fix(self, errors: List[str], project_path: str, job_info: Dict[str, Any]) -> Any:
        """
        多轮策略修复 - 当AI生成失败时的备用方案

        Args:
            errors: 错误列表
            project_path: 项目路径
            job_info: 作业信息

        Returns:
            修复结果
        """
        try:
            logger.info("🔄 使用多轮策略修复...")

            from bot_agent.tools.intelligent_tool_coordinator import global_tool_coordinator

            # 分析错误类型
            error_categories = global_tool_coordinator._categorize_errors(errors)

            fix_results = []
            successful_fixes = 0

            # 针对每种错误类型，使用多轮交互修复
            for category, category_errors in error_categories.items():
                if not category_errors:
                    continue

                logger.info(f"🔧 处理 {category} 类型错误: {len(category_errors)} 个")

                # 为每种错误类型生成多个修复尝试
                for attempt in range(3):  # 最多3次尝试
                    logger.info(f"🔄 {category} 第 {attempt + 1} 次修复尝试")

                    if category == 'lint_errors':
                        result = await self._multi_round_lint_fix(category_errors, project_path, attempt)
                    elif category == 'dependency_errors':
                        result = await self._multi_round_dependency_fix(category_errors, project_path, attempt)
                    elif category == 'build_errors':
                        result = await self._multi_round_build_fix(category_errors, project_path, attempt)
                    else:
                        result = await self._multi_round_generic_fix(category_errors, project_path, attempt)

                    fix_results.append(result)

                    if result.get('success', False):
                        successful_fixes += 1
                        logger.info(f"✅ {category} 修复成功")
                        break  # 成功后跳出重试循环
                    else:
                        logger.warning(f"❌ {category} 第 {attempt + 1} 次尝试失败")

            return type('ToolResult', (), {
                'success': successful_fixes > 0,
                'message': f"多轮策略修复完成，成功修复 {successful_fixes} 种错误类型",
                'data': {
                    'fix_results': fix_results,
                    'successful_fixes': successful_fixes,
                    'multi_round_strategy': True
                }
            })()

        except Exception as e:
            logger.error(f"多轮策略修复失败: {e}")
            return type('ToolResult', (), {
                'success': False,
                'message': f"多轮策略修复失败: {str(e)}",
                'data': {}
            })()

    async def _generate_alternative_fix(self, original_step: Dict[str, Any], failed_result: Dict[str, Any], project_path: str) -> Dict[str, Any]:
        """
        为失败的关键步骤生成替代修复方案

        Args:
            original_step: 原始修复步骤
            failed_result: 失败结果
            project_path: 项目路径

        Returns:
            替代修复结果
        """
        try:
            logger.info("🤖 生成替代修复方案...")

            from bot_agent.tools.intelligent_tool_coordinator import global_tool_coordinator

            # 分析失败原因
            error_message = failed_result.get('message', '')
            original_command = original_step.get('command', '')
            description = original_step.get('description', '')

            # 使用AI生成替代命令
            alternative_command = await global_tool_coordinator._get_ai_alternative_command(
                original_command=original_command,
                error_message=error_message,
                description=description,
                project_path=project_path
            )

            if alternative_command:
                logger.info(f"🔄 尝试替代命令: {alternative_command}")

                # 执行替代命令
                alternative_step = {
                    **original_step,
                    'command': alternative_command,
                    'description': f"替代方案: {description}"
                }

                return await global_tool_coordinator._execute_ai_fix_step(alternative_step, project_path)
            else:
                return {
                    'success': False,
                    'message': '无法生成替代修复方案',
                    'description': description
                }

        except Exception as e:
            logger.error(f"生成替代修复方案失败: {e}")
            return {
                'success': False,
                'message': f'替代方案生成失败: {str(e)}',
                'description': original_step.get('description', '')
            }

    async def _multi_round_lint_fix(self, errors: List[str], project_path: str, attempt: int) -> Dict[str, Any]:
        """多轮Lint错误修复"""
        try:
            from bot_agent.tools.intelligent_tool_coordinator import global_tool_coordinator

            # 根据尝试次数使用不同的修复策略
            if attempt == 0:
                # 第1次：尝试修复配置文件
                command = "Get-ChildItem -Path . -Recurse -File | Select-String -Pattern 'extend-ignore.*#'"
                description = "查找并修复flake8配置错误"
            elif attempt == 1:
                # 第2次：尝试重新生成配置
                command = "echo '[flake8]' > .flake8 && echo 'extend-ignore = E203, W503' >> .flake8"
                description = "重新生成正确的flake8配置"
            else:
                # 第3次：尝试删除有问题的配置
                command = "Remove-Item .flake8 -ErrorAction SilentlyContinue"
                description = "删除有问题的flake8配置文件"

            # 使用多轮重试执行
            return await global_tool_coordinator._execute_command_with_retry(
                command=command,
                description=description,
                project_path=project_path,
                max_retries=2
            )

        except Exception as e:
            return {'success': False, 'message': f'Lint修复失败: {str(e)}'}

    async def _multi_round_dependency_fix(self, errors: List[str], project_path: str, attempt: int) -> Dict[str, Any]:
        """多轮依赖错误修复"""
        try:
            from bot_agent.tools.intelligent_tool_coordinator import global_tool_coordinator

            if attempt == 0:
                command = "pip install --upgrade pip"
                description = "升级pip版本"
            elif attempt == 1:
                command = "pip install -r requirements.txt --force-reinstall"
                description = "强制重新安装依赖"
            else:
                command = "pip check"
                description = "检查依赖完整性"

            return await global_tool_coordinator._execute_command_with_retry(
                command=command,
                description=description,
                project_path=project_path,
                max_retries=2
            )

        except Exception as e:
            return {'success': False, 'message': f'依赖修复失败: {str(e)}'}

    async def _multi_round_build_fix(self, errors: List[str], project_path: str, attempt: int) -> Dict[str, Any]:
        """多轮构建错误修复"""
        try:
            from bot_agent.tools.intelligent_tool_coordinator import global_tool_coordinator

            if attempt == 0:
                command = "python -m py_compile *.py"
                description = "检查Python语法"
            elif attempt == 1:
                command = "python setup.py check"
                description = "检查setup.py配置"
            else:
                command = "python -c 'import sys; print(sys.version)'"
                description = "检查Python版本"

            return await global_tool_coordinator._execute_command_with_retry(
                command=command,
                description=description,
                project_path=project_path,
                max_retries=2
            )

        except Exception as e:
            return {'success': False, 'message': f'构建修复失败: {str(e)}'}

    async def _multi_round_generic_fix(self, errors: List[str], project_path: str, attempt: int) -> Dict[str, Any]:
        """多轮通用错误修复"""
        try:
            from bot_agent.tools.intelligent_tool_coordinator import global_tool_coordinator

            if attempt == 0:
                command = "Get-ChildItem -Path . -Recurse -File -Include '*.log' | Select-Object -First 5"
                description = "查找错误日志文件"
            elif attempt == 1:
                command = "python --version"
                description = "检查Python环境"
            else:
                command = "Get-Location"
                description = "确认当前工作目录"

            return await global_tool_coordinator._execute_command_with_retry(
                command=command,
                description=description,
                project_path=project_path,
                max_retries=2
            )

        except Exception as e:
            return {'success': False, 'message': f'通用修复失败: {str(e)}'}
