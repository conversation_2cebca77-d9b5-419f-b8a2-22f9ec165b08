#!/usr/bin/env python3
"""
AI解决方案自动执行器
将AI的分析结果转换为可执行的操作
"""

import re
import json
import logging
import asyncio
import subprocess
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class ExecutableAction:
    """可执行的操作"""
    action_type: str  # 'command', 'file_edit', 'git_operation'
    description: str
    command: Optional[str] = None
    file_path: Optional[str] = None
    file_content: Optional[str] = None
    working_directory: Optional[str] = None
    expected_result: Optional[str] = None


class AISolutionExecutor:
    """AI解决方案执行器"""
    
    def __init__(self):
        self.execution_log = []
    
    def parse_ai_response(self, ai_response: str) -> List[ExecutableAction]:
        """
        解析AI响应，提取可执行的操作
        
        Args:
            ai_response: AI的响应文本
            
        Returns:
            List[ExecutableAction]: 可执行操作列表
        """
        actions = []
        
        try:
            # 1. 提取代码块中的命令
            code_blocks = self._extract_code_blocks(ai_response)
            for code_block in code_blocks:
                if code_block['language'] in ['bash', 'shell', 'cmd']:
                    actions.extend(self._parse_shell_commands(code_block['content']))
                elif code_block['language'] == 'python':
                    actions.extend(self._parse_python_code(code_block['content']))
            
            # 2. 提取文件操作指令
            file_operations = self._extract_file_operations(ai_response)
            actions.extend(file_operations)
            
            # 3. 提取修复建议并转换为操作
            fix_suggestions = self._extract_fix_suggestions(ai_response)
            actions.extend(fix_suggestions)
            
            logger.info(f"从AI响应中解析出 {len(actions)} 个可执行操作")
            return actions
            
        except Exception as e:
            logger.error(f"解析AI响应失败: {e}")
            return []
    
    def _extract_code_blocks(self, text: str) -> List[Dict[str, str]]:
        """提取代码块"""
        code_blocks = []
        
        # 匹配 ```language 和 ``` 之间的内容
        pattern = r'```(\w+)?\n(.*?)\n```'
        matches = re.findall(pattern, text, re.DOTALL)
        
        for language, content in matches:
            code_blocks.append({
                'language': language.lower() if language else 'text',
                'content': content.strip()
            })
        
        return code_blocks
    
    def _parse_shell_commands(self, content: str) -> List[ExecutableAction]:
        """解析shell命令"""
        actions = []
        lines = content.split('\n')
        
        for line in lines:
            line = line.strip()
            if line and not line.startswith('#'):
                actions.append(ExecutableAction(
                    action_type='command',
                    description=f"执行命令: {line}",
                    command=line
                ))
        
        return actions
    
    def _parse_python_code(self, content: str) -> List[ExecutableAction]:
        """解析Python代码"""
        actions = []
        
        # 检查是否是工具调用
        if 'tool_coordinator' in content:
            actions.append(ExecutableAction(
                action_type='tool_call',
                description="执行工具协调器调用",
                command=content
            ))
        else:
            # 普通Python代码
            actions.append(ExecutableAction(
                action_type='python_script',
                description="执行Python脚本",
                command=content
            ))
        
        return actions
    
    def _extract_file_operations(self, text: str) -> List[ExecutableAction]:
        """提取文件操作"""
        actions = []
        
        # 查找文件修改指令
        file_patterns = [
            r'修改文件\s*[`"]([^`"]+)[`"]',
            r'编辑\s*[`"]([^`"]+)[`"]',
            r'创建文件\s*[`"]([^`"]+)[`"]'
        ]
        
        for pattern in file_patterns:
            matches = re.findall(pattern, text)
            for file_path in matches:
                actions.append(ExecutableAction(
                    action_type='file_edit',
                    description=f"修改文件: {file_path}",
                    file_path=file_path
                ))
        
        return actions
    
    def _extract_fix_suggestions(self, text: str) -> List[ExecutableAction]:
        """提取修复建议并转换为操作"""
        actions = []
        
        # 常见的修复建议模式
        fix_patterns = {
            r'运行\s*[`"]([^`"]+)[`"]': 'command',
            r'执行\s*[`"]([^`"]+)[`"]': 'command',
            r'安装\s*[`"]([^`"]+)[`"]': 'command',
            r'pip install\s+([^\s]+)': 'command'
        }
        
        for pattern, action_type in fix_patterns.items():
            matches = re.findall(pattern, text)
            for match in matches:
                if action_type == 'command':
                    actions.append(ExecutableAction(
                        action_type='command',
                        description=f"执行修复命令: {match}",
                        command=match
                    ))
        
        return actions
    
    async def execute_actions(self, actions: List[ExecutableAction], project_path: str) -> Dict[str, Any]:
        """
        执行操作列表
        
        Args:
            actions: 操作列表
            project_path: 项目路径
            
        Returns:
            Dict[str, Any]: 执行结果
        """
        results = {
            'total_actions': len(actions),
            'successful_actions': 0,
            'failed_actions': 0,
            'execution_log': [],
            'final_status': 'unknown'
        }
        
        for i, action in enumerate(actions, 1):
            logger.info(f"执行操作 {i}/{len(actions)}: {action.description}")
            
            try:
                result = await self._execute_single_action(action, project_path)
                
                if result['success']:
                    results['successful_actions'] += 1
                    logger.info(f"✅ 操作成功: {action.description}")
                else:
                    results['failed_actions'] += 1
                    logger.error(f"❌ 操作失败: {action.description} - {result['error']}")
                
                results['execution_log'].append({
                    'action': action.description,
                    'success': result['success'],
                    'output': result.get('output', ''),
                    'error': result.get('error', '')
                })
                
            except Exception as e:
                results['failed_actions'] += 1
                error_msg = f"执行异常: {str(e)}"
                logger.error(f"❌ {action.description} - {error_msg}")
                
                results['execution_log'].append({
                    'action': action.description,
                    'success': False,
                    'output': '',
                    'error': error_msg
                })
        
        # 计算最终状态
        success_rate = results['successful_actions'] / results['total_actions'] if results['total_actions'] > 0 else 0
        if success_rate >= 0.8:
            results['final_status'] = 'success'
        elif success_rate >= 0.5:
            results['final_status'] = 'partial_success'
        else:
            results['final_status'] = 'failed'
        
        logger.info(f"执行完成: {results['successful_actions']}/{results['total_actions']} 成功")
        return results
    
    async def _execute_single_action(self, action: ExecutableAction, project_path: str) -> Dict[str, Any]:
        """执行单个操作"""
        try:
            if action.action_type == 'command':
                return await self._execute_command(action.command, project_path)
            elif action.action_type == 'tool_call':
                return await self._execute_tool_call(action.command, project_path)
            elif action.action_type == 'python_script':
                return await self._execute_python_script(action.command, project_path)
            elif action.action_type == 'file_edit':
                return await self._execute_file_edit(action, project_path)
            else:
                return {'success': False, 'error': f'未知操作类型: {action.action_type}'}
                
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def _execute_command(self, command: str, project_path: str) -> Dict[str, Any]:
        """执行shell命令"""
        try:
            process = await asyncio.create_subprocess_shell(
                command,
                cwd=project_path,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            return {
                'success': process.returncode == 0,
                'output': stdout.decode('utf-8', errors='ignore'),
                'error': stderr.decode('utf-8', errors='ignore') if process.returncode != 0 else ''
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def _execute_tool_call(self, code: str, project_path: str) -> Dict[str, Any]:
        """执行工具调用"""
        try:
            from bot_agent.tools.intelligent_tool_coordinator import global_tool_coordinator
            
            # 创建执行环境
            exec_globals = {
                'tool_coordinator': global_tool_coordinator,
                'print': lambda *args: logger.info(' '.join(str(arg) for arg in args))
            }
            
            # 执行代码
            exec(code, exec_globals)
            
            return {'success': True, 'output': '工具调用执行完成'}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def _execute_python_script(self, code: str, project_path: str) -> Dict[str, Any]:
        """执行Python脚本"""
        try:
            # 在项目目录下执行Python代码
            import tempfile
            import os
            
            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as temp_file:
                temp_file.write(code)
                temp_script_path = temp_file.name
            
            try:
                process = await asyncio.create_subprocess_exec(
                    'python', temp_script_path,
                    cwd=project_path,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                
                stdout, stderr = await process.communicate()
                
                return {
                    'success': process.returncode == 0,
                    'output': stdout.decode('utf-8', errors='ignore'),
                    'error': stderr.decode('utf-8', errors='ignore') if process.returncode != 0 else ''
                }
            finally:
                os.unlink(temp_script_path)
                
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def _execute_file_edit(self, action: ExecutableAction, project_path: str) -> Dict[str, Any]:
        """执行文件编辑"""
        try:
            # 这里可以集成更复杂的文件编辑逻辑
            # 目前只是标记为成功
            return {'success': True, 'output': f'文件编辑标记: {action.file_path}'}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}


# 全局实例
ai_solution_executor = AISolutionExecutor()
