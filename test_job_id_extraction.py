#!/usr/bin/env python3
"""
测试Job ID提取功能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_job_id_extraction():
    """测试Job ID提取"""
    print("🔍 测试Job ID提取功能...")
    
    try:
        from bot_agent.engines.task_executor import AiderBasedTaskExecutor
        
        executor = AiderBasedTaskExecutor()
        
        # 测试用例
        test_cases = [
            ("智能分析Job 870的失败原因并执行修复", 870),
            ("Job 123 failure analysis", 123),
            ("分析作业ID: 456的错误", 456),
            ("分析作业ID：789的错误", 789),  # 中文全角冒号
            ("作业ID: 999", 999),
            ("无Job ID的任务", None)
        ]
        
        success_count = 0
        
        for title, expected_job_id in test_cases:
            extracted_id = executor._extract_job_id(title, "")
            if extracted_id == expected_job_id:
                print(f"✅ Job ID提取正确: '{title}' → {extracted_id}")
                success_count += 1
            else:
                print(f"❌ Job ID提取错误: '{title}' → {extracted_id} (期望: {expected_job_id})")
        
        print(f"\n📊 测试结果: {success_count}/{len(test_cases)} 通过")
        
        if success_count == len(test_cases):
            print("🎉 所有Job ID提取测试通过！")
            return True
        else:
            print("⚠️ 部分Job ID提取测试失败")
            return False
        
    except Exception as e:
        print(f"❌ Job ID提取测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_job_id_extraction()
    sys.exit(0 if success else 1)
